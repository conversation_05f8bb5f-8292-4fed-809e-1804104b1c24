{
  "extends": "@electron-toolkit/tsconfig/tsconfig.web.json",
  "include": [
    "src/renderer/src/**/*",
    "src/preload/*.d.ts",
    "local/src/renderer/**/*",
    "packages/shared/**/*",
    "tests/__mocks__/**/*"
  ],
  "types": [
    "vitest/globals",
    "@testing-library/jest-dom"
  ],
  "typeRoots": [
    "./src/preload"
  ],
  "compilerOptions": {
    "composite": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@renderer/*": [
        "src/renderer/src/*"
      ],
      "@shared/*": [
        "packages/shared/*"
      ],
      "@types": [
        "src/renderer/src/infrastructure/types"
      ],
      "@logger": [
        "src/renderer/src/business/services/logger.service"
      ],
    }
  }
}