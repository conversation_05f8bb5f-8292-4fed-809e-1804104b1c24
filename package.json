{"name": "echolab", "version": "1.0.0-alpha.1", "description": "EchoLab is a video player designed for language learners, helping users learn foreign languages efficiently through sentence-by-sentence intensive listening.", "main": "./out/main/index.js", "author": "echolab.vip", "homepage": "https://echolab.vip", "scripts": {"format": "prettier --write .", "format:check": "prettier --check .", "lint": "eslint --cache .", "lint:fix": "eslint --cache --fix .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:win:x64": "npm run build && electron-builder --win --x64", "build:win:arm64": "npm run build && electron-builder --win --arm64", "build:mac": "electron-vite build && electron-builder --mac", "build:mac:x64": "npm run build && electron-builder --mac --x64", "build:mac:arm64": "npm run build && electron-builder --mac --arm64", "build:linux": "electron-vite build && electron-builder --linux", "build:linux:x64": "npm run build && electron-builder --linux --x64", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "npm run build && playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "version:current": "tsx scripts/version-manager.ts current", "version:set": "tsx scripts/version-manager.ts set", "version:major": "tsx scripts/version-manager.ts major", "version:minor": "tsx scripts/version-manager.ts minor", "version:patch": "tsx scripts/version-manager.ts patch", "version:prerelease": "tsx scripts/version-manager.ts prerelease", "version:beta": "tsx scripts/version-manager.ts minor beta", "version:beta-patch": "tsx scripts/version-manager.ts patch beta", "release": "npm run build && electron-builder --publish onTagOrDraft", "release:all": "npm run build && electron-builder --publish always", "release:never": "npm run build && electron-builder --publish never", "release:draft": "npm run build && electron-builder --publish onTagOrDraft", "release:rename": "tsx scripts/rename-artifacts.ts", "release:auto": "tsx scripts/release.ts", "release:check": "tsx scripts/pre-release-check.ts", "prepare": "husky", "check:i18n": "tsx scripts/check-i18n.ts", "sync:i18n": "tsx scripts/sync-i18n.ts", "update:i18n": "dotenv -e .env -- tsx scripts/update-i18n.ts", "auto:i18n": "dotenv -e .env -- tsx scripts/auto-translate-i18n.ts"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@types/react-virtualized": "^9.22.2", "antd": "^5.26.6", "dompurify": "^3.2.6", "electron-conf": "^1.3.0", "electron-log": "^5.4.1", "electron-updater": "^6.6.2", "immer": "^10.1.1", "macos-release": "^3.4.0", "marked": "^15.0.12", "react-hotkeys-hook": "^5.1.0", "react-player": "^2.16.1", "react-virtualized": "^9.22.6", "zustand": "^5.0.6"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.1.0", "@electron-toolkit/tsconfig": "^1.0.1", "@eslint-react/eslint-plugin": "^1.52.3", "@playwright/test": "^1.54.1", "@swc/plugin-styled-components": "^9.0.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/cli-progress": "^3.11.6", "@types/dompurify": "^3.2.0", "@types/node": "^22.16.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/yaml": "^1.9.7", "@vitejs/plugin-react": "^4.7.0", "@vitejs/plugin-react-swc": "^3.11.0", "@vitest/coverage-v8": "^2.1.9", "@vitest/ui": "^2.1.9", "cli-progress": "^3.12.0", "code-inspector-plugin": "^1.0.2", "dexie": "^4.0.11", "dexie-react-hooks": "^1.1.7", "electron": "37.2.4", "electron-builder": "26.0.19", "electron-icon-maker": "^0.0.5", "electron-vite": "^3.1.0", "electron-window-state": "^5.0.3", "eslint": "^9.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "framer-motion": "^12.23.12", "husky": "^9.1.7", "jschardet": "^3.1.4", "jsdom": "^25.0.1", "linguist-languages": "^8.0.0", "lint-staged": "^16.1.2", "lucide-react": "^0.536.0", "msw": "^2.10.4", "openai": "^5.11.0", "prettier": "^3.6.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.1", "react-router-dom": "^7.7.1", "sass": "^1.89.2", "styled-components": "^6.1.19", "tsx": "^4.20.3", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^2.1.9", "winston-daily-rotate-file": "^5.0.0", "yaml": "^2.8.0"}, "pnpm": {"onlyBuiltDependencies": ["electron", "esbuild"], "overrides": {"brace-expansion": "^2.0.1", "minimatch": "^9.0.5", "builder-util-runtime": "9.3.3"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --cache --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}