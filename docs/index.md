---
# https://vitepress.dev/reference/default-theme-home-page
layout: home

hero:
  name: 'EchoLab'
  tagline: 专为语言学习者设计的智能视频播放器
  actions:
    - theme: brand
      text: 快速入门
      link: ./user-guide/quick-start

features:
  - title: 🎯 逐句精听系统
    details: 一键跳转上/下句字幕，自动暂停，单句循环，专为语言学习设计的核心功能
  - title: 📚 智能字幕系统
    details: 支持 SRT、VTT、ASS、JSON 多种格式，自动检测，双语显示，拖拽调整位置
  - title: 📽️ 专业播放控制
    details: 0.25x-2.0x 变速播放，精确跳转，音量控制，丰富的快捷键操作
---

## 什么是 EchoLab？

EchoLab 是一款强大的桌面应用程序，专为语言学习而设计。它提供直观的界面和先进的逐句精听功能，让您的外语学习更加高效和专业。

## 核心功能

- **🎯 逐句精听系统** - 一键跳转上/下句，自动暂停，单句循环
- **📽️ 专业播放控制** - 变速播放，精确跳转，丰富快捷键
- **📚 智能字幕系统** - 多格式支持，自动检测，双语显示
- **🌍 跨平台支持** - 支持 Windows、macOS 和 Linux

## 快速开始

<div class="vp-doc">
  <div class="custom-block tip">
    <p class="custom-block-title">EchoLab 新用户？</p>
    <p>从我们的 <a href="./user-guide/quick-start">快速入门</a> 开始，5 分钟内掌握核心功能。</p>
  </div>
</div>

## 文档导航

### 📖 [用户指南](./user-guide/)

完整的用户手册，涵盖安装、界面、功能和故障排除。

### 🚀 [快速入门](./user-guide/quick-start)

5 分钟掌握 EchoLab 核心功能，开始高效语言学习。

### 📋 [安装指南](./user-guide/installation)

详细的安装步骤，支持 Windows、macOS 和 Linux 系统。

## 获取帮助

- **使用问题**: 查看[常见问题解答](./user-guide/faq)或[故障排除](./user-guide/troubleshooting)
- **技术支持**: 访问[技术支持](./user-guide/troubleshooting)获取官方帮助
- **问题报告**: 在我们的 [GitHub 仓库](https://github.com/mkdir700/echolab/issues)提交问题

## 社区

加入我们的社区，获取帮助、分享技巧，并为 EchoLab 的发展做出贡献：

- [GitHub Discussions](https://github.com/mkdir700/echolab/discussions) - 讨论交流
- [Issue Tracker](https://github.com/mkdir700/echolab/issues) - 问题追踪
- **邮箱支持**: <EMAIL>

## 版本信息

- **当前版本**: v0.1.0-beta.1
- **发布日期**: 2025年6月4日
- **开发状态**: Beta 测试版
- **支持平台**: Windows 10/11, macOS 10.15+, Linux (Ubuntu 20.04+)

---

_如果您觉得 EchoLab 对您有帮助，请给我们一个 ⭐️ [Star](https://github.com/mkdir700/echolab)，这是对我们最大的鼓励！_
