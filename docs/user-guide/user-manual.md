# 完整用户手册

欢迎使用 EchoLab！这是一款专为语言学习者设计的智能视频播放器，让您的外语学习更加高效和专业。本手册将详细介绍如何使用 EchoLab 的各项功能。

## 📖 目录

1. [快速开始](#快速开始)
2. [界面介绍](#界面介绍)
3. [核心功能详解](#核心功能详解)
4. [快捷键操作](#快捷键操作)
5. [文件格式支持](#文件格式支持)
6. [常见问题解答](#常见问题解答)
7. [使用技巧](#使用技巧)

---

## 🚀 快速开始

### 第一步：启动应用

1. 双击桌面上的 EchoLab 图标启动应用
2. 首次启动时，应用会进行初始化设置
3. 进入主界面后，您将看到简洁的播放器界面

### 第二步：导入视频文件

**方法一：拖拽导入**

- 直接将视频文件拖拽到 EchoLab 窗口中
- 支持同时拖入视频和字幕文件

**方法二：菜单导入**

- 点击"打开文件"按钮
- 在文件选择器中选择您的视频文件
- 点击"打开"确认

**方法三：最近播放**

- 在主界面查看最近播放的视频列表
- 点击任意视频即可快速重新播放

### 第三步：加载字幕

**自动检测**

- EchoLab 会自动检测与视频同名的字幕文件
- 支持的字幕格式：SRT、VTT、ASS/SSA、JSON

**手动加载**

- 点击字幕设置按钮
- 选择"加载字幕文件"
- 浏览并选择字幕文件

### 第四步：开始学习

- 点击播放按钮开始观看
- 使用逐句精听功能进行深度学习
- 根据需要调整播放速度和字幕显示模式

---

## 🖥️ 界面介绍

### 主播放区域

**视频播放器**

- 位于界面中央，显示视频内容
- 支持全屏播放和窗口模式
- 可通过鼠标点击控制播放/暂停

**字幕显示区域**

- 位于视频下方，显示当前字幕内容
- 支持拖拽调整位置
- 可切换显示模式（原文/译文/双语）

### 控制面板

**播放控制区**

- 播放/暂停按钮
- 进度条显示播放进度
- 时间显示（当前时间/总时长）

**功能按钮区**

- 上一句/下一句字幕按钮
- 单句循环开关
- 自动暂停开关
- 播放速度调节
- 音量控制
- 字幕模式切换

### 侧边栏

**文件管理**

- 最近播放列表
- 播放历史记录
- 收藏夹（即将推出）

**设置选项**

- 播放偏好设置
- 字幕样式设置
- 快捷键自定义

---

## ⭐ 核心功能详解

### 🎯 逐句精听系统

这是 EchoLab 的核心功能，专为语言学习设计。

**一键跳转功能**

- **上一句字幕**：点击 `H` 键或点击"上一句"按钮
- **下一句字幕**：点击 `L` 键或点击"下一句"按钮
- **精确定位**：自动跳转到字幕的准确开始时间

**使用场景**：

- 听不清某句话时，快速回到上一句重听
- 想要跳过已掌握的内容，快速前进到下一句
- 逐句分析语法和词汇

**自动暂停功能**

- **开启方式**：按 `P` 键或点击自动暂停按钮
- **工作原理**：每句字幕结束后自动暂停播放
- **学习优势**：给您充分时间思考和理解内容

**使用技巧**：

- 初学者建议开启自动暂停，便于消化理解
- 熟练后可关闭自动暂停，保持流畅观看体验
- 结合单句循环使用效果更佳

**单句循环功能**

- **开启方式**：按 `R` 键或点击循环按钮
- **循环范围**：当前字幕的开始到结束时间
- **循环次数**：无限循环，直到手动关闭

**最佳实践**：

1. 遇到难懂的句子时开启单句循环
2. 重复听 3-5 遍，注意语音语调
3. 理解后关闭循环，继续下一句

### 📽️ 专业播放控制

**变速播放**

- **速度范围**：0.25x - 2.0x
- **调节方式**：
  - 点击播放速度按钮选择预设速度
  - 使用快捷键微调速度
- **推荐设置**：
  - 初学者：0.5x - 0.75x（慢速理解）
  - 中级学习者：0.75x - 1.0x（正常速度）
  - 高级学习者：1.0x - 1.25x（挑战听力）

**精确跳转**

- **10秒后退**：按 `←` 键
- **10秒前进**：按 `→` 键
- **用途**：
  - 快速回听刚才的内容
  - 跳过不重要的片段
  - 精确定位到特定时间点

**音量控制**

- **调节方式**：
  - 使用 `↑/↓` 方向键
  - 点击音量按钮拖拽滑块
- **静音功能**：点击音量图标快速静音

### 📚 智能字幕系统

**多格式支持**

EchoLab 支持主流字幕格式：

| 格式    | 扩展名    | 特点               | 推荐用途   |
| ------- | --------- | ------------------ | ---------- |
| SRT     | .srt      | 最常用，兼容性好   | 日常学习   |
| VTT     | .vtt      | Web标准，支持样式  | 在线视频   |
| ASS/SSA | .ass/.ssa | 高级样式，特效丰富 | 动画、电影 |
| JSON    | .json     | 自定义格式         | 特殊需求   |

**自动检测机制**

- 检测规则：视频文件名 + 字幕扩展名
- 示例：
  - 视频：`lesson1.mp4`
  - 字幕：`lesson1.srt`（自动检测）
  - 字幕：`lesson1.zh.srt`（中文字幕）
  - 字幕：`lesson1.en.srt`（英文字幕）

**字幕显示模式**

1. **原文模式**

   - 只显示原语言字幕
   - 适合高级学习者
   - 提高语言沉浸感

2. **译文模式**

   - 只显示翻译字幕
   - 适合理解剧情
   - 降低学习压力

3. **双语模式**
   - 同时显示原文和译文
   - 适合中级学习者
   - 便于对照学习

**字幕位置调整**

- **拖拽调整**：鼠标拖拽字幕到合适位置
- **重置位置**：双击字幕区域恢复默认位置
- **保存设置**：位置设置会自动保存

### 🗂️ 学习管理

**播放记录功能**

- **自动保存**：每次播放都会自动记录进度
- **精确恢复**：下次打开时自动跳转到上次位置
- **多文件支持**：每个视频文件独立记录进度

**文件管理**

- **最近播放**：显示最近观看的视频列表
- **快速访问**：点击即可重新播放
- **清理功能**：可手动清理播放历史

**进度恢复**

- **智能提醒**：重新打开文件时询问是否继续播放
- **一键跳转**：快速跳转到上次播放位置
- **新开始选项**：也可选择从头开始播放

---

## ⌨️ 快捷键操作

掌握快捷键可以大大提高学习效率：

### 基础播放控制

| 功能       | 快捷键   | 说明           |
| ---------- | -------- | -------------- |
| 播放/暂停  | `空格键` | 最常用的控制键 |
| 音量增大   | `↑`      | 每次增加 5%    |
| 音量减小   | `↓`      | 每次减少 5%    |
| 快进 10 秒 | `→`      | 跳过不重要内容 |
| 快退 10 秒 | `←`      | 重听刚才内容   |

### 逐句精听专用

| 功能       | 快捷键 | 说明             |
| ---------- | ------ | ---------------- |
| 上一句字幕 | `H`    | 跳转到上一句开始 |
| 下一句字幕 | `L`    | 跳转到下一句开始 |
| 单句循环   | `R`    | 重复播放当前句   |
| 自动暂停   | `P`    | 每句结束后暂停   |

### 高级功能

| 功能         | 快捷键 | 说明              |
| ------------ | ------ | ----------------- |
| 全屏切换     | `F`    | 进入/退出全屏     |
| 字幕模式切换 | `S`    | 原文/译文/双语    |
| 播放速度重置 | `1`    | 恢复 1.0x 速度    |
| 静音切换     | `M`    | 快速静音/取消静音 |

### 快捷键使用技巧

**组合使用**

- `H` + `R`：回到上一句并开启循环
- `P` + `R`：开启自动暂停和单句循环
- `S` + `L`：切换字幕模式并前进到下一句

**自定义快捷键**

- 进入设置 → 快捷键设置
- 点击要修改的功能
- 按下新的快捷键组合
- 保存设置

---

## 📁 文件格式支持

### 视频格式

EchoLab 支持几乎所有主流视频格式：

**常见格式**

- **MP4**：最推荐，兼容性最好
- **AVI**：经典格式，广泛支持
- **MKV**：高质量，支持多音轨
- **MOV**：苹果格式，质量优秀
- **WMV**：Windows 媒体格式
- **FLV**：Flash 视频格式

**高清支持**

- **4K 视频**：3840×2160 分辨率
- **1080P 高清**：1920×1080 分辨率
- **720P 高清**：1280×720 分辨率

**编码兼容性**

- **H.264**：最常用编码
- **H.265/HEVC**：新一代高效编码
- **VP9**：Google 开发的开源编码
- **AV1**：最新的开源编码标准

### 字幕格式详解

**SRT 格式（推荐）**

```
1
00:00:01,000 --> 00:00:03,000
Hello, welcome to EchoLab!

2
00:00:04,000 --> 00:00:06,000
Let's start learning together.
```

- 最简单易用的格式
- 兼容性最好
- 适合大多数学习场景

**VTT 格式**

```
WEBVTT

00:00:01.000 --> 00:00:03.000
Hello, welcome to EchoLab!

00:00:04.000 --> 00:00:06.000
Let's start learning together.
```

- Web 标准格式
- 支持样式设置
- 适合在线视频

**ASS/SSA 格式**

- 支持复杂样式和特效
- 可设置字体、颜色、位置
- 适合动画和电影

**JSON 格式**

```json
{
  "subtitles": [
    {
      "start": 1000,
      "end": 3000,
      "text": "Hello, welcome to EchoLab!"
    }
  ]
}
```

- 自定义格式
- 便于程序处理
- 支持扩展字段

### 文件导入最佳实践

**文件命名建议**

- 视频和字幕使用相同文件名
- 多语言字幕添加语言标识
- 示例：
  - `lesson1.mp4`（视频）
  - `lesson1.srt`（字幕）
  - `lesson1.en.srt`（英文字幕）
  - `lesson1.zh.srt`（中文字幕）

**文件组织建议**

- 将相关文件放在同一文件夹
- 按课程或主题分类
- 使用有意义的文件夹名称

---

## ❓ 常见问题解答

### 安装和启动问题

**Q: 安装时提示安全警告怎么办？**

A: 这是正常现象，因为 EchoLab 是新软件。解决方法：

- **Windows**：点击"更多信息" → "仍要运行"
- **macOS**：系统偏好设置 → 安全性与隐私 → 点击"仍要打开"
- **Linux**：确保文件有执行权限

**Q: 启动后界面显示异常？**

A: 可能的解决方案：

1. 重启应用程序
2. 检查显卡驱动是否最新
3. 尝试以管理员权限运行
4. 联系技术支持

### 视频播放问题

**Q: 视频无法播放或显示黑屏？**

A: 检查以下几点：

1. **格式支持**：确认视频格式在支持列表中
2. **编码问题**：某些特殊编码可能不支持
3. **文件损坏**：尝试用其他播放器测试
4. **自动转换**：EchoLab 可能会提示转换格式

**Q: 播放卡顿或不流畅？**

A: 优化建议：

1. **降低播放速度**：先用较慢速度播放
2. **关闭其他程序**：释放系统资源
3. **检查硬盘空间**：确保有足够空间
4. **更新显卡驱动**：提升硬件加速性能

### 字幕相关问题

**Q: 字幕不显示或显示乱码？**

A: 解决步骤：

1. **检查文件编码**：确保字幕文件是 UTF-8 编码
2. **手动加载**：通过菜单手动选择字幕文件
3. **格式检查**：确认字幕格式正确
4. **重新下载**：尝试重新获取字幕文件

**Q: 字幕时间不同步？**

A: 调整方法：

1. **检查字幕文件**：确认时间轴是否正确
2. **手动调整**：使用字幕编辑功能微调
3. **重新匹配**：寻找匹配的字幕版本
4. **自制字幕**：考虑自己制作字幕

### 功能使用问题

**Q: 快捷键不起作用？**

A: 检查要点：

1. **焦点问题**：确保 EchoLab 窗口处于活动状态
2. **冲突检查**：是否与系统快捷键冲突
3. **重置设置**：尝试重置快捷键设置
4. **重启应用**：重启后再次尝试

**Q: 播放记录丢失？**

A: 可能原因：

1. **文件移动**：视频文件位置发生变化
2. **权限问题**：应用没有写入权限
3. **设置重置**：应用设置被重置
4. **存储空间**：系统存储空间不足

---

## 💡 使用技巧

### 高效学习方法

**分层学习法**

1. **第一遍**：正常速度，了解大意
2. **第二遍**：开启自动暂停，逐句理解
3. **第三遍**：使用单句循环，攻克难点
4. **第四遍**：提高速度，检验理解

**重点突破法**

1. 标记不理解的句子
2. 使用单句循环反复听
3. 查阅相关语法和词汇
4. 模仿发音和语调

**对比学习法**

1. 使用双语字幕对比
2. 注意翻译差异
3. 理解文化背景
4. 积累表达方式

### 字幕使用策略

**初学者策略**

- 开始时使用双语字幕
- 逐渐减少对译文的依赖
- 最终过渡到纯原文字幕

**中级学习者策略**

- 主要使用原文字幕
- 遇到困难时切换到双语
- 重点关注语法结构

**高级学习者策略**

- 完全使用原文字幕
- 关注语音语调变化
- 学习地道表达方式

### 播放控制技巧

**速度调节策略**

- **0.5x**：适合精听训练
- **0.75x**：适合理解内容
- **1.0x**：正常学习速度
- **1.25x**：挑战听力极限

**循环使用技巧**

- 短句循环：理解语法结构
- 长句循环：把握语音节奏
- 段落循环：理解上下文关系

### 学习进度管理

**制定学习计划**

1. 设定每日学习时间
2. 选择合适难度的材料
3. 记录学习进度
4. 定期复习重点内容

**进度跟踪方法**

- 记录每天学习的视频数量
- 统计掌握的新词汇
- 评估听力理解程度
- 设定阶段性目标

### 故障排除技巧

**性能优化**

1. 关闭不必要的后台程序
2. 定期清理系统垃圾文件
3. 确保有足够的内存空间
4. 更新系统和驱动程序

**文件管理**

1. 定期整理视频和字幕文件
2. 备份重要的学习材料
3. 使用云存储同步文件
4. 建立清晰的文件夹结构

---

## 🔧 高级设置

### 播放偏好设置

**默认播放速度**

- 设置启动时的默认播放速度
- 可选择 0.25x 到 2.0x 之间的任意值
- 建议根据个人水平设置

**自动暂停设置**

- 设置是否默认开启自动暂停
- 可调整暂停后的等待时间
- 支持按内容类型设置不同策略

**循环播放设置**

- 设置单句循环的默认次数
- 可选择无限循环或指定次数
- 支持循环结束后的自动操作

### 字幕显示设置

**字体设置**

- 选择字幕字体类型
- 调整字体大小
- 设置字体粗细

**颜色设置**

- 字幕文字颜色
- 背景颜色和透明度
- 边框和阴影效果

**位置设置**

- 默认字幕显示位置
- 多行字幕的行间距
- 字幕与视频边缘的距离

### 快捷键自定义

**修改快捷键**

1. 进入设置 → 快捷键
2. 点击要修改的功能
3. 按下新的按键组合
4. 保存设置

**快捷键冲突处理**

- 系统会自动检测冲突
- 提示选择保留哪个设置
- 建议避免与系统快捷键冲突

**导入导出设置**

- 可导出快捷键配置文件
- 支持在不同设备间同步设置
- 可恢复默认快捷键设置

---

## 📞 技术支持

### 获取帮助

**官方渠道**

- **GitHub Issues**：[提交问题报告](https://github.com/mkdir700/echolab/issues)
- **邮箱支持**：<EMAIL>
- **项目主页**：https://github.com/mkdir700/echolab

**社区支持**

- **GitHub Discussions**：与其他用户交流
- **用户论坛**：分享使用经验
- **QQ 群/微信群**：即时交流（即将开放）

### 问题反馈

**Bug 报告**
请提供以下信息：

1. 操作系统版本
2. EchoLab 版本号
3. 详细的问题描述
4. 重现步骤
5. 相关截图或日志

**功能建议**
欢迎提出：

1. 新功能想法
2. 界面改进建议
3. 性能优化建议
4. 用户体验改进

### 更新和升级

**自动更新**

- EchoLab 会自动检查更新
- 有新版本时会提示下载
- 可在设置中关闭自动检查

**手动更新**

1. 访问官方 GitHub 页面
2. 下载最新版本
3. 卸载旧版本
4. 安装新版本

**版本说明**

- **Beta 版本**：功能完整但可能有 Bug
- **正式版本**：稳定可靠的发布版本
- **开发版本**：最新功能但不够稳定

---

## 🎉 结语

感谢您选择 EchoLab 作为您的语言学习伙伴！我们致力于为语言学习者提供最好的工具和体验。

**持续改进**

- 我们会根据用户反馈持续改进产品
- 定期发布新功能和性能优化
- 始终保持对用户需求的关注

**社区建设**

- 欢迎加入 EchoLab 用户社区
- 分享您的学习经验和技巧
- 与其他语言学习者交流互动

**未来展望**

- 更多语言学习辅助功能
- AI 驱动的智能学习建议
- 跨平台同步和云端存储
- 更丰富的学习统计和分析

**开始您的学习之旅**
现在就打开 EchoLab，导入您的第一个学习视频，开始高效的语言学习之旅吧！

如果您觉得 EchoLab 对您有帮助，请给我们一个 ⭐️ Star，这是对我们最大的鼓励！

---

_本手册会随着 EchoLab 的更新而持续完善，请关注最新版本。_
