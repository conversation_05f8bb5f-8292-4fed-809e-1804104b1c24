# 视频导入 | Video Import

学会如何导入和管理视频文件是使用 EchoLab 的第一步。本页面将详细介绍各种导入方法和文件格式支持。

## 🚀 导入方法

### 方法一：拖拽导入（推荐）

最简单快捷的导入方式：

1. **直接拖拽**：将视频文件从文件管理器拖拽到 EchoLab 窗口中
2. **批量导入**：可同时拖入多个视频文件
3. **字幕同步**：支持同时拖入视频和字幕文件

**使用技巧**：

- 拖拽时会显示导入提示区域
- 支持从任何位置拖拽文件
- 可以拖拽整个文件夹

### 方法二：菜单导入

通过界面按钮导入：

1. 点击主界面的"打开文件"按钮
2. 在文件选择器中浏览并选择视频文件
3. 点击"打开"确认导入

**适用场景**：

- 精确选择特定文件
- 浏览复杂的文件夹结构
- 需要预览文件信息

### 方法三：最近播放

快速访问历史文件：

1. 在主界面查看最近播放列表
2. 点击任意视频即可快速重新播放
3. 支持搜索和筛选功能

## 📁 文件格式支持

### 视频格式

EchoLab 支持几乎所有主流视频格式：

#### 常见格式

| 格式 | 扩展名 | 兼容性     | 推荐度     | 说明               |
| ---- | ------ | ---------- | ---------- | ------------------ |
| MP4  | .mp4   | ⭐⭐⭐⭐⭐ | 🔥🔥🔥🔥🔥 | 最推荐，兼容性最好 |
| AVI  | .avi   | ⭐⭐⭐⭐   | 🔥🔥🔥     | 经典格式，广泛支持 |
| MKV  | .mkv   | ⭐⭐⭐⭐⭐ | 🔥🔥🔥🔥   | 高质量，支持多音轨 |
| MOV  | .mov   | ⭐⭐⭐⭐   | 🔥🔥🔥     | 苹果格式，质量优秀 |
| WMV  | .wmv   | ⭐⭐⭐     | 🔥🔥       | Windows 媒体格式   |
| FLV  | .flv   | ⭐⭐       | 🔥         | Flash 视频格式     |

#### 高清支持

- **4K 视频**：3840×2160 分辨率
- **1080P 高清**：1920×1080 分辨率
- **720P 高清**：1280×720 分辨率

#### 编码兼容性

- **H.264**：最常用编码，兼容性最好
- **H.265/HEVC**：新一代高效编码，文件更小
- **VP9**：Google 开发的开源编码
- **AV1**：最新的开源编码标准

### 字幕格式

#### 支持的字幕格式

| 格式    | 扩展名    | 特点               | 推荐用途   |
| ------- | --------- | ------------------ | ---------- |
| SRT     | .srt      | 最常用，兼容性好   | 日常学习   |
| VTT     | .vtt      | Web标准，支持样式  | 在线视频   |
| ASS/SSA | .ass/.ssa | 高级样式，特效丰富 | 动画、电影 |
| JSON    | .json     | 自定义格式         | 特殊需求   |

#### SRT 格式示例

```srt
1
00:00:01,000 --> 00:00:03,000
Hello, welcome to EchoLab!

2
00:00:04,000 --> 00:00:06,000
Let's start learning together.
```

#### VTT 格式示例

```vtt
WEBVTT

00:00:01.000 --> 00:00:03.000
Hello, welcome to EchoLab!

00:00:04.000 --> 00:00:06.000
Let's start learning together.
```

#### JSON 格式示例

```json
{
  "subtitles": [
    {
      "start": 1000,
      "end": 3000,
      "text": "Hello, welcome to EchoLab!"
    },
    {
      "start": 4000,
      "end": 6000,
      "text": "Let's start learning together."
    }
  ]
}
```

## 🔄 自动转换功能

### 格式兼容性处理

当遇到不支持的视频格式时，EchoLab 会：

1. **自动检测**：识别视频编码和格式
2. **转换提示**：询问是否进行格式转换
3. **后台处理**：使用内置 FFmpeg 进行转换
4. **进度显示**：实时显示转换进度

### 转换设置

**质量选项**：

- **高质量**：保持原始质量，文件较大
- **平衡模式**：质量和大小的平衡
- **快速模式**：转换速度优先，质量略降

**输出格式**：

- 默认转换为 MP4 格式
- 可选择其他兼容格式
- 支持自定义参数设置

## 📂 文件管理

### 自动检测机制

EchoLab 会自动检测与视频同名的字幕文件：

**检测规则**：

- 视频文件名 + 字幕扩展名
- 支持多语言标识

**命名示例**：

```
lesson1.mp4              # 视频文件
lesson1.srt              # 自动检测的字幕
lesson1.zh.srt           # 中文字幕
lesson1.en.srt           # 英文字幕
lesson1.zh-CN.srt        # 简体中文字幕
lesson1.en-US.srt        # 美式英语字幕
```

### 文件组织建议

**推荐的文件夹结构**：

```
学习资料/
├── 英语学习/
│   ├── 美剧/
│   │   ├── Friends/
│   │   │   ├── S01E01.mp4
│   │   │   ├── S01E01.srt
│   │   │   └── S01E01.zh.srt
│   │   └── ...
│   └── 纪录片/
│       ├── BBC/
│       └── National Geographic/
└── 日语学习/
    ├── 动画/
    └── 电影/
```

**命名规范建议**：

1. **使用有意义的文件名**

   - ✅ `Friends_S01E01_The_One_Where_Monica_Gets_a_Roommate.mp4`
   - ❌ `video1.mp4`

2. **统一命名格式**

   - 剧集：`剧名_季数集数_集名.mp4`
   - 电影：`电影名_年份.mp4`
   - 课程：`课程名_章节_标题.mp4`

3. **语言标识清晰**
   - 中文字幕：`.zh.srt` 或 `.chs.srt`
   - 英文字幕：`.en.srt`
   - 日文字幕：`.ja.srt`

### 播放记录管理

**自动记录功能**：

- 每次播放都会自动记录进度
- 记录播放时间、进度位置
- 保存字幕设置和播放速度

**记录信息包含**：

- 文件路径和名称
- 最后播放时间
- 播放进度（时间点）
- 字幕显示模式
- 播放速度设置
- 音量设置

**隐私保护**：

- 所有记录仅保存在本地
- 不会上传到任何服务器
- 可随时清除播放历史

## ⚠️ 常见问题

### 导入问题

**Q: 拖拽文件没有反应？**

A: 检查以下几点：

1. 确保文件格式在支持列表中
2. 检查文件是否损坏
3. 尝试重启应用程序
4. 确认文件没有被其他程序占用

**Q: 视频导入后无法播放？**

A: 可能的解决方案：

1. 检查视频编码是否支持
2. 尝试使用格式转换功能
3. 确认文件完整性
4. 更新显卡驱动程序

### 字幕问题

**Q: 字幕文件无法自动检测？**

A: 检查以下设置：

1. 确认文件名是否匹配
2. 检查字幕文件编码（推荐 UTF-8）
3. 尝试手动加载字幕
4. 确认字幕格式是否支持

**Q: 字幕显示乱码？**

A: 解决步骤：

1. 将字幕文件转换为 UTF-8 编码
2. 检查字幕文件是否完整
3. 尝试其他字幕文件
4. 重新下载字幕

### 性能问题

**Q: 大文件导入很慢？**

A: 优化建议：

1. 关闭不必要的后台程序
2. 确保有足够的磁盘空间
3. 使用 SSD 硬盘可提升速度
4. 考虑压缩视频文件

## 💡 使用技巧

### 批量处理

**批量导入**：

- 选择多个文件同时拖拽
- 使用 Ctrl+A 全选文件夹内容
- 支持不同格式混合导入

**批量转换**：

- 可同时转换多个视频文件
- 设置统一的输出参数
- 后台队列处理，不影响使用

### 快速访问

**收藏功能**：

- 将常用视频添加到收藏夹
- 支持自定义分类标签
- 快速搜索和筛选

**快捷键**：

- `Ctrl+O`：打开文件对话框
- `Ctrl+R`：刷新最近播放列表
- `Ctrl+F`：搜索文件

### 工作流优化

**学习项目管理**：

1. 为每个学习项目创建专门文件夹
2. 使用统一的命名规范
3. 定期整理和备份文件
4. 利用播放记录跟踪进度

**多设备同步**：

- 使用云存储同步文件
- 导出播放记录和设置
- 在不同设备间保持一致性

---

## 下一步

现在您已经掌握了视频导入的方法，接下来可以：

1. [了解字幕编辑功能](./subtitle-editing.md)
2. [学习快捷键操作](./keyboard-shortcuts.md)
3. [探索导出和分享功能](./export.md)

准备好开始您的学习之旅了吗？导入您的第一个视频文件，开始体验 EchoLab 的强大功能吧！
