# 故障排除

本页面提供 EchoLab 常见问题的解决方案。

## 常见问题

### 视频无法播放

**问题描述**: 导入视频后无法播放或显示黑屏

**可能原因**:

- 视频格式不支持
- 视频文件损坏
- 缺少必要的编解码器

**解决方案**:

1. 确认视频格式是否支持（推荐 MP4、WebM、OGV）
2. 尝试使用其他播放器验证视频文件是否正常
3. 重新转换视频格式
4. 检查视频文件路径是否包含特殊字符

### 字幕无法加载

**问题描述**: 字幕文件导入后不显示或显示异常

**可能原因**:

- 字幕文件格式不支持
- 字幕文件编码问题
- 字幕时间轴与视频不匹配

**解决方案**:

1. 确认字幕格式（支持 SRT、VTT、ASS、JSON）
2. 检查字幕文件编码（推荐 UTF-8）
3. 验证字幕时间轴是否正确
4. 尝试重新生成或编辑字幕文件

### 应用启动失败

**问题描述**: EchoLab 无法启动或启动后立即崩溃

**可能原因**:

- 系统不兼容
- 缺少运行时依赖
- 配置文件损坏

**解决方案**:

1. 检查系统要求是否满足
2. 重新安装应用程序
3. 清除应用数据和配置文件
4. 以管理员权限运行（Windows）

### 性能问题

**问题描述**: 播放卡顿、响应缓慢或内存占用过高

**可能原因**:

- 视频分辨率过高
- 系统资源不足
- 后台程序占用资源

**解决方案**:

1. 降低视频分辨率或码率
2. 关闭其他不必要的程序
3. 增加系统内存
4. 检查硬盘空间是否充足

## 技术支持

如果以上解决方案无法解决您的问题，请通过以下方式获取帮助：

### GitHub Issues

在我们的 [GitHub 仓库](https://github.com/mkdir700/echolab/issues) 提交问题报告。

提交问题时请包含：

- 操作系统版本
- EchoLab 版本
- 详细的问题描述
- 重现步骤
- 错误截图或日志

### 邮箱支持

发送邮件至：<EMAIL>

### 社区讨论

参与 [GitHub Discussions](https://github.com/mkdir700/echolab/discussions) 与其他用户交流。

## 日志文件位置

如需提交技术支持请求，请提供相关日志文件：

**Windows**:

```
%APPDATA%\EchoLab\logs\
```

**macOS**:

```
~/Library/Logs/EchoLab/
```

**Linux**:

```
~/.config/EchoLab/logs/
```

## 重置应用

如果遇到严重问题，可以尝试重置应用到初始状态：

1. 关闭 EchoLab
2. 删除配置目录：
   - Windows: `%APPDATA%\EchoLab\`
   - macOS: `~/Library/Application Support/EchoLab/`
   - Linux: `~/.config/EchoLab/`
3. 重新启动应用

**注意**: 重置会清除所有设置和用户数据，请谨慎操作。
