# 快捷键操作 | Keyboard Shortcuts

掌握快捷键是提高 EchoLab 使用效率的关键。本页面详细介绍了所有可用的快捷键操作，帮助您更流畅地进行语言学习。

## 默认快捷键 / Default Shortcuts

### 播放控制 / Playback Control

| 功能         | 默认快捷键 | 说明              |
| ------------ | ---------- | ----------------- |
| 播放/暂停    | `Space`    | 控制视频播放状态  |
| 后退5秒      | `←`        | 视频后退5秒       |
| 前进5秒      | `→`        | 视频前进5秒       |
| 音量增加     | `↑`        | 增加音量10%       |
| 音量减少     | `↓`        | 减少音量10%       |
| 播放速度增加 | `]`        | 加快播放速度      |
| 播放速度减少 | `[`        | 减慢播放速度      |
| 重置播放速度 | `\`        | 重置播放速度为1倍 |

### 字幕控制 / Subtitle Control

| 功能         | 默认快捷键 | 说明                        |
| ------------ | ---------- | --------------------------- |
| 切换字幕模式 | `Ctrl+M`   | 在原文/双语/隐藏字幕间切换  |
| 单句循环     | `R`        | 开启/关闭当前字幕单句循环   |
| 自动暂停     | `Ctrl+P`   | 开启/关闭字幕结束时自动暂停 |
| 上一句字幕   | `H`        | 跳转到上一句字幕            |
| 下一句字幕   | `L`        | 跳转到下一句字幕            |

### 界面控制 / Interface Control

| 功能         | 默认快捷键     | 说明                               |
| ------------ | -------------- | ---------------------------------- |
| 切换全屏模式 | `F`            | 在应用内切换全屏/窗口模式          |
| 退出全屏模式 | `Escape`       | 在全屏模式下按 ESC 键退出全屏      |
| 重置字幕设置 | `Ctrl+Shift+R` | 重置字幕位置、大小和背景为默认配置 |

## 平台差异 / Platform Differences

### macOS 用户

在 macOS 系统中，快捷键显示会使用 Apple 标准符号：

- `⌘` 代表 Cmd 键
- `⌥` 代表 Alt/Option 键
- `⇧` 代表 Shift 键
- `↩` 代表 Enter 键

### Windows/Linux 用户

在 Windows 和 Linux 系统中，快捷键显示使用标准文本：

- `Ctrl` 代表 Ctrl 键
- `Alt` 代表 Alt 键
- `Shift` 代表 Shift 键
- `↵` 代表 Enter 键

## 自定义快捷键 / Customizing Shortcuts

### 访问设置 / Accessing Settings

1. 打开 EchoLab 应用
2. 进入 **设置** 页面
3. 选择 **快捷键** 选项卡

### 修改快捷键 / Modifying Shortcuts

1. **点击编辑按钮**：在要修改的快捷键右侧点击编辑图标 📝
2. **输入新快捷键**：在弹出的对话框中按下您想要设置的快捷键组合
3. **确认更改**：按 `Enter` 确认，或按 `Escape` 取消

### 快捷键设置规则 / Shortcut Rules

#### 支持的修饰键 / Supported Modifiers

- `Ctrl` (macOS 上为 `Cmd`)
- `Alt` (macOS 上为 `Option`)
- `Shift`

#### 支持的按键 / Supported Keys

- **字母键**：A-Z
- **数字键**：0-9
- **方向键**：↑ ↓ ← →
- **特殊键**：Space（空格键）
- **符号键**：`[` `]` `\` 等

#### 禁用的按键 / Forbidden Keys

以下按键为系统保留，无法设置为快捷键：

- `Enter`, `Escape`, `Tab`
- `Backspace`, `Delete`, `Insert`
- `Home`, `End`, `PageUp`, `PageDown`
- `CapsLock`, `NumLock`, `ScrollLock`
- `PrintScreen`, `Pause`, `ContextMenu`
- 功能键 `F1`-`F12`

## 冲突处理 / Conflict Resolution

### 自动检测 / Automatic Detection

EchoLab 会自动检测快捷键冲突：

- 当您设置新快捷键时，系统会立即检查是否与现有快捷键冲突
- 如果发现冲突，会显示警告信息并阻止设置

### 冲突解决 / Resolving Conflicts

当检测到冲突时：

1. **查看冲突详情**：系统会显示具体的冲突信息
2. **选择解决方案**：
   - 修改当前快捷键为其他组合
   - 或者修改冲突的快捷键
3. **自动修复**：点击"重置为默认"可自动解决所有冲突

### 自动修复机制 / Auto-Fix Mechanism

应用启动时会自动检查并修复快捷键冲突：

- 优先保留使用默认快捷键的功能
- 将冲突的自定义快捷键重置为默认值
- 修复结果会自动保存

## 配置管理 / Configuration Management

### 重置设置 / Reset Settings

点击 **"重置为默认"** 按钮可以：

- 清除所有自定义快捷键设置
- 恢复到出厂默认配置
- 解决所有可能的冲突

### 导出配置 / Export Configuration

点击 **"导出配置"** 按钮可以：

- 将当前快捷键配置保存为 JSON 文件
- 文件名格式：`echolab-shortcuts.json`
- 包含时间戳信息，便于版本管理

### 数据存储 / Data Storage

- 快捷键配置自动保存到本地存储
- 应用重启后配置会自动加载
- 支持跨会话保持设置

## 使用技巧 / Usage Tips

### 高效学习建议 / Efficient Learning Tips

1. **熟悉基础快捷键**：

   - 先掌握播放控制（空格、方向键）
   - 再学习字幕控制（R、H、L）

2. **自定义常用功能**：

   - 根据个人习惯调整快捷键
   - 将最常用的功能设置为最容易按的键

3. **利用字幕功能**：

   - 使用 `R` 键进行单句循环练习
   - 使用 `Ctrl+P` 开启自动暂停，便于思考

4. **速度控制技巧**：
   - 使用 `[` `]` 调整播放速度
   - 使用 `\` 快速重置到正常速度

### 快捷键组合建议 / Recommended Combinations

- **听力练习**：开启自动暂停 + 单句循环
- **跟读练习**：适当降低播放速度 + 字幕模式切换
- **复习模式**：隐藏字幕 + 快速跳转上下句

## 故障排除 / Troubleshooting

### 快捷键不响应 / Shortcuts Not Working

1. **检查焦点**：确保 EchoLab 窗口处于活动状态
2. **检查冲突**：查看设置中是否有冲突警告
3. **重置配置**：尝试重置为默认设置

### 无法设置某些快捷键 / Cannot Set Certain Shortcuts

1. **检查禁用列表**：确认按键不在禁用列表中
2. **系统快捷键冲突**：某些快捷键可能被系统占用
3. **输入法冲突**：切换到英文输入法后再设置

### 配置丢失 / Configuration Lost

1. **检查本地存储**：浏览器可能清除了本地数据
2. **重新导入**：如果有导出的配置文件，可以手动恢复
3. **重新设置**：重新配置常用的快捷键

---

## 下一步

掌握了快捷键操作后，您可以：

1. [返回概览](./overview.md) - 查看所有功能
2. [视频导入](./video-import.md) - 了解如何导入视频
3. [字幕编辑](./subtitle-editing.md) - 掌握字幕功能
4. [导出分享](./export.md) - 分享学习成果

通过合理使用和自定义快捷键，您可以大大提高在 EchoLab 中的学习效率。建议根据个人使用习惯调整快捷键配置，打造最适合自己的学习环境。
