# 快速入门

欢迎使用 EchoLab！这份快速指南将帮助您在 5 分钟内掌握核心功能，开始高效的语言学习之旅。

## 🎯 核心亮点

EchoLab 是专为语言学习者设计的智能视频播放器，具备以下核心功能：

- **逐句精听**：一键跳转上/下句，自动暂停，单句循环
- **智能字幕**：支持多格式，自动检测，双语显示
- **专业控制**：变速播放，精确跳转，快捷键操作
- **学习管理**：播放记录，进度恢复，文件管理

---

## ⚡ 3 分钟快速上手

### 步骤 1：导入视频（30 秒）

**拖拽导入**（推荐）

- 直接将视频文件拖入 EchoLab 窗口
- 同时拖入字幕文件（可选）

**或点击导入**

- 点击"打开文件"按钮
- 选择您的视频文件

### 步骤 2：加载字幕（30 秒）

**自动检测**

- EchoLab 自动检测同名字幕文件
- 支持格式：SRT、VTT、ASS、JSON

**手动加载**

- 点击字幕设置按钮
- 选择"加载字幕文件"

### 步骤 3：开始学习（2 分钟）

**基础操作**

- `空格键`：播放/暂停
- `H`：上一句字幕
- `L`：下一句字幕
- `R`：单句循环
- `P`：自动暂停

**学习模式**

1. 开启自动暂停（按 `P`）
2. 逐句播放，每句结束自动停止
3. 遇到难句开启循环（按 `R`）
4. 重复听 3-5 遍直到理解

---

## 🎮 必备快捷键

| 功能         | 快捷键    | 说明         |
| ------------ | --------- | ------------ |
| **播放控制** |
| 播放/暂停    | `空格`    | 基础控制     |
| 快退/快进    | `←` / `→` | 10秒跳转     |
| 音量调节     | `↑` / `↓` | 增减音量     |
| **逐句精听** |
| 上一句       | `H`       | 跳到上句开始 |
| 下一句       | `L`       | 跳到下句开始 |
| 单句循环     | `R`       | 重复当前句   |
| 自动暂停     | `P`       | 句末自动停   |

---

## 📚 字幕显示模式

点击字幕模式按钮或按 `S` 键切换：

1. **原文模式** - 只显示原语言（适合高级学习者）
2. **译文模式** - 只显示翻译（适合理解剧情）
3. **双语模式** - 同时显示原文和译文（推荐）

---

## 🎵 播放速度调节

根据学习水平选择合适速度：

- **0.5x - 0.75x**：初学者，慢速理解
- **0.75x - 1.0x**：中级学习者，正常速度
- **1.0x - 1.25x**：高级学习者，挑战听力

点击播放速度按钮选择预设速度。

---

## 💡 学习技巧

### 推荐学习流程

1. **第一遍**：正常速度，了解大意
2. **第二遍**：开启自动暂停，逐句理解
3. **第三遍**：使用单句循环，攻克难点
4. **第四遍**：提高速度，检验理解

### 字幕使用策略

**初学者**

- 使用双语字幕
- 开启自动暂停
- 多用单句循环

**中级学习者**

- 主要用原文字幕
- 困难时切换双语
- 关注语法结构

**高级学习者**

- 纯原文字幕
- 关注语音语调
- 学习地道表达

---

## 📁 支持格式

### 视频格式

- **推荐**：MP4, MKV, MOV
- **支持**：AVI, WMV, FLV
- **分辨率**：支持 4K/1080P/720P

### 字幕格式

- **SRT**：最常用，推荐
- **VTT**：Web 标准
- **ASS/SSA**：高级样式
- **JSON**：自定义格式

---

## ⚠️ Beta 版本注意事项

### ✅ 已验证功能

- 逐句精听系统
- 主流视频/字幕格式
- 基础播放控制
- 快捷键操作

### 🚧 已知限制

- 部分高级字幕样式可能显示不完整
- 某些视频编码可能存在播放问题
- 界面细节仍在优化中
- 性能在某些设备上需要进一步优化

### 💬 反馈渠道

- **GitHub Issues**：[报告问题](https://github.com/mkdir700/echolab/issues)
- **邮箱**：<EMAIL>
- **讨论区**：[GitHub Discussions](https://github.com/mkdir700/echolab/discussions)

---

## 🚀 下一步

完成快速入门后，您可以：

1. **深入学习**：阅读[完整用户手册](./user-manual)
2. **掌握快捷键**：查看[快捷键大全](../keyboard-shortcuts)
3. **解决问题**：访问[常见问题](./user-manual#常见问题解答)
4. **分享反馈**：帮助我们改进产品

---

**开始您的语言学习之旅吧！** 🎉

> 如果遇到任何问题，请查看[完整用户手册](./user-manual)或联系我们的技术支持。
