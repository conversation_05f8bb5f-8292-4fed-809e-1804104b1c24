# 字幕编辑 | Subtitle Editing

字幕是语言学习的重要工具。EchoLab 提供了强大的字幕功能，帮助您更好地理解和学习外语内容。

## 📚 字幕显示模式

### 三种显示模式

EchoLab 支持三种字幕显示模式，适应不同学习阶段的需求：

#### 1. 原文模式 (Original)

**特点**：

- 只显示原语言字幕
- 提供最纯粹的语言环境
- 适合高级学习者

**使用场景**：

- 语言水平较高时使用
- 想要完全沉浸在目标语言中
- 测试自己的理解能力

**学习效果**：

- 提高语言沉浸感
- 锻炼纯语言理解能力
- 避免对翻译的依赖

#### 2. 译文模式 (Translation)

**特点**：

- 只显示翻译字幕
- 帮助理解剧情和内容
- 降低学习压力

**使用场景**：

- 初次观看复杂内容
- 需要快速理解剧情
- 休闲观看时使用

**学习效果**：

- 快速理解内容大意
- 减少学习焦虑
- 为后续精学做准备

#### 3. 双语模式 (Bilingual)

**特点**：

- 同时显示原文和译文
- 便于对照学习
- 平衡理解和学习

**使用场景**：

- 中级学习者的主要模式
- 学习新词汇和表达
- 对照语法结构

**学习效果**：

- 建立语言对应关系
- 学习地道表达方式
- 理解文化差异

### 模式切换

**快捷键切换**：

- 按 `S` 键快速切换模式
- 循环顺序：原文 → 译文 → 双语 → 原文

**按钮切换**：

- 点击字幕模式按钮
- 显示当前模式状态
- 支持鼠标悬停预览

**自动记忆**：

- 每个视频独立记忆模式设置
- 下次播放时自动恢复
- 支持全局默认设置

## 🎯 字幕导航功能

### 逐句跳转

这是 EchoLab 的核心功能，专为语言学习设计：

#### 上一句字幕

- **快捷键**：`H`
- **功能**：跳转到上一句字幕的开始时间
- **用途**：重听没理解的内容

#### 下一句字幕

- **快捷键**：`L`
- **功能**：跳转到下一句字幕的开始时间
- **用途**：跳过已掌握的内容

#### 精确定位

- 自动跳转到字幕的准确开始时间
- 避免手动拖拽进度条的不精确
- 确保完整听到每句话的开头

### 学习辅助功能

#### 自动暂停

- **开启方式**：按 `P` 键或点击自动暂停按钮
- **工作原理**：每句字幕结束后自动暂停播放
- **学习优势**：
  - 给您充分时间思考和理解
  - 避免错过重要内容
  - 控制学习节奏

**使用技巧**：

- 初学者建议开启自动暂停
- 熟练后可关闭保持流畅性
- 结合单句循环使用效果更佳

#### 单句循环

- **开启方式**：按 `R` 键或点击循环按钮
- **循环范围**：当前字幕的开始到结束时间
- **循环次数**：无限循环，直到手动关闭

**最佳实践**：

1. 遇到难懂的句子时开启单句循环
2. 重复听 3-5 遍，注意语音语调
3. 理解后关闭循环，继续下一句
4. 可配合变速播放使用

## ✏️ 字幕编辑功能

### 位置调整

**拖拽调整**：

- 鼠标拖拽字幕到合适位置
- 实时预览调整效果
- 支持精确像素级定位

**重置位置**：

- 双击字幕区域恢复默认位置
- 或使用右键菜单选择重置
- 支持多显示器环境

**位置保存**：

- 位置设置自动保存
- 每个视频独立记忆
- 支持导出导入设置

### 样式自定义

#### 字体设置

- **字体类型**：选择系统已安装字体
- **字体大小**：支持 12px - 72px 范围
- **字体粗细**：正常、粗体、超粗体
- **字体样式**：正常、斜体

#### 颜色设置

- **文字颜色**：支持自定义颜色选择
- **背景颜色**：可设置半透明背景
- **边框颜色**：增强文字可读性
- **阴影效果**：提供多种阴影样式

#### 布局设置

- **行间距**：调整多行字幕间距
- **字符间距**：微调字符显示效果
- **对齐方式**：左对齐、居中、右对齐
- **边距设置**：与视频边缘的距离

### 时间轴调整

**微调功能**：

- 支持字幕时间的精确调整
- 可以提前或延后字幕显示
- 精度可达毫秒级别

**批量调整**：

- 整体提前或延后所有字幕
- 保持相对时间关系不变
- 适用于字幕文件时间轴偏移

**同步检测**：

- 自动检测字幕与音频的同步性
- 提供同步建议和修正方案
- 支持手动标记同步点

## 🔍 字幕搜索功能

### 内容搜索

**关键词搜索**：

- 在当前字幕中搜索特定词汇
- 支持模糊匹配和精确匹配
- 高亮显示搜索结果

**跳转功能**：

- 点击搜索结果直接跳转
- 支持上一个/下一个结果导航
- 显示搜索结果的上下文

**搜索历史**：

- 保存最近的搜索记录
- 快速重复搜索
- 支持搜索记录管理

### 学习笔记

**标记功能**：

- 标记重要的字幕句子
- 添加个人学习笔记
- 支持不同颜色分类

**导出笔记**：

- 导出标记的句子和笔记
- 支持多种格式（TXT、PDF、Word）
- 便于复习和整理

## 📖 学习策略指南

### 不同水平的使用策略

#### 初学者策略 (A1-A2)

**推荐设置**：

- 使用双语字幕模式
- 开启自动暂停功能
- 播放速度设为 0.5x - 0.75x

**学习步骤**：

1. 第一遍：看译文理解大意
2. 第二遍：对照原文学习词汇
3. 第三遍：尝试只看原文
4. 第四遍：关闭字幕测试理解

**重点关注**：

- 基础词汇的发音
- 简单语法结构
- 常用表达方式

#### 中级学习者策略 (B1-B2)

**推荐设置**：

- 主要使用原文字幕
- 适度使用自动暂停
- 播放速度设为 0.75x - 1.0x

**学习步骤**：

1. 第一遍：原文字幕正常播放
2. 第二遍：遇到困难切换双语
3. 第三遍：使用单句循环攻克难点
4. 第四遍：提高速度检验理解

**重点关注**：

- 复杂语法结构
- 习惯用语和俚语
- 语音语调变化

#### 高级学习者策略 (C1-C2)

**推荐设置**：

- 完全使用原文字幕
- 关闭自动暂停
- 播放速度设为 1.0x - 1.25x

**学习步骤**：

1. 第一遍：正常速度观看
2. 第二遍：关注细节和语调
3. 第三遍：分析修辞和文化内容
4. 第四遍：模仿发音和表达

**重点关注**：

- 地道表达方式
- 文化背景知识
- 语言的艺术性

### 专项训练方法

#### 听力训练

**渐进式训练**：

1. 双语字幕 → 原文字幕 → 无字幕
2. 慢速播放 → 正常速度 → 快速播放
3. 简单内容 → 复杂内容 → 专业内容

**重复训练**：

- 使用单句循环功能
- 每句重复 3-5 遍
- 注意语音细节变化

#### 词汇学习

**上下文学习**：

- 在字幕中标记生词
- 理解词汇在语境中的含义
- 学习词汇的搭配用法

**词汇笔记**：

- 记录新学词汇
- 包含例句和用法
- 定期复习和测试

#### 语法分析

**句型分析**：

- 暂停在复杂句子上
- 分析语法结构
- 对比中英文表达差异

**语法笔记**：

- 记录特殊语法现象
- 收集典型例句
- 总结语法规律

## ⚙️ 高级设置

### 字幕偏好设置

**默认显示模式**：

- 设置启动时的默认字幕模式
- 可按内容类型设置不同默认值
- 支持全局和单文件设置

**自动加载设置**：

- 设置字幕文件的自动检测规则
- 优先级设置（语言偏好）
- 编码检测和转换设置

**显示时机设置**：

- 字幕提前显示时间
- 字幕延后消失时间
- 空白字幕的处理方式

### 性能优化

**渲染优化**：

- 硬件加速字幕渲染
- 减少重绘次数
- 优化大字幕文件处理

**内存管理**：

- 字幕缓存策略
- 自动清理无用数据
- 内存使用监控

## ⚠️ 常见问题

### 字幕显示问题

**Q: 字幕不显示或显示乱码？**

A: 解决步骤：

1. 检查字幕文件编码（推荐 UTF-8）
2. 确认字幕格式是否支持
3. 尝试手动加载字幕文件
4. 检查字幕文件是否完整

**Q: 字幕时间不同步？**

A: 调整方法：

1. 使用时间轴调整功能
2. 检查字幕文件版本是否匹配
3. 尝试重新下载字幕
4. 使用同步检测功能

### 功能使用问题

**Q: 快捷键不起作用？**

A: 检查要点：

1. 确保 EchoLab 窗口处于活动状态
2. 检查是否与系统快捷键冲突
3. 尝试重置快捷键设置
4. 重启应用程序

**Q: 字幕位置无法调整？**

A: 可能原因：

1. 检查是否开启了位置锁定
2. 确认鼠标拖拽操作正确
3. 尝试双击重置位置
4. 检查显示器分辨率设置

## 💡 使用技巧

### 效率提升技巧

**快捷键组合**：

- `H` + `R`：回到上一句并开启循环
- `P` + `R`：开启自动暂停和单句循环
- `S` + `L`：切换字幕模式并前进到下一句

**批量操作**：

- 批量调整字幕样式
- 批量导出学习笔记
- 批量处理字幕文件

### 学习效果优化

**制定学习计划**：

1. 设定每日学习时间和内容
2. 选择合适难度的材料
3. 记录学习进度和效果
4. 定期复习和测试

**多感官学习**：

- 听觉：专注语音语调
- 视觉：观察字幕和画面
- 动觉：跟读和模仿
- 记忆：做笔记和总结

---

## 下一步

掌握了字幕功能后，您可以：

1. [学习快捷键操作](./keyboard-shortcuts.md) - 提高操作效率
2. [了解导出功能](./export.md) - 分享学习成果
3. [返回概览](./overview.md) - 查看其他功能

现在就开始使用字幕功能，让您的语言学习更加高效和有趣吧！
