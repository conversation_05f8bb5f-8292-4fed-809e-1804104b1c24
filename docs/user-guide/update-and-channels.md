# 更新机制与渠道说明

> 本文介绍 EchoLab 的自动更新机制、支持的更新渠道、常见用户交互与故障排查方法。

---

## 🚀 自动更新简介

EchoLab 支持自动检查和下载安装新版本，确保您始终体验到最新功能和修复。

- 启动时自动检查更新
- 设置页面可手动检查更新（点击"检查更新"按钮）
- 检查到新版本时会弹出更新提示
- 支持后台自动下载和一键安装

---

## 🛠️ 支持的更新渠道

EchoLab 提供多种更新渠道，满足不同用户需求：

| 渠道   | 说明                                 | 适用人群        |
| ------ | ------------------------------------ | --------------- |
| stable | 稳定版，经过充分测试，推荐大多数用户 | 所有用户        |
| beta   | 公测版，优先体验新功能，可能有小问题 | 喜欢尝鲜        |
| alpha  | 内测版，功能前沿，可能不稳定         | 高级用户/开发者 |
| dev    | 开发版，最新实验特性，风险较高       | 开发者/测试     |

---

## 🔢 版本号与渠道关系

EchoLab 会根据版本号自动识别渠道，无需手动切换。

- `1.2.3` → stable
- `1.2.3-beta`、`1.2.3-beta.1` → beta
- `1.2.3-alpha`、`1.2.3-alpha.2` → alpha
- `1.2.3-dev`、`1.2.3-dev.5` → dev

**举例:**

- 当前版本 `0.5.0-beta.2`，则自动使用 beta 渠道获取更新
- 当前版本 `1.0.0`，则只接收 stable 渠道的更新

---

## 🖱️ 用户交互与常见问题

### 1. 如何手动检查更新？

- 打开"设置"页面，点击"检查更新"按钮
- 有新版本时会弹出更新对话框
- 支持后台下载、跳过此版本、强制更新等

### 2. 红点通知代表什么？

- 自动检查到新版本时，"检查更新"按钮会显示红点提醒
- 点击后红点消失，表示已知晓
- 跳过某个版本后，该版本红点不再提示

### 3. 什么是强制更新？

- 某些重要版本会强制要求更新，无法跳过
- 更新对话框会隐藏"跳过"按钮，必须下载并安装

### 4. 如何切换更新渠道？

- 普通用户无需手动切换，系统会自动识别
- 高级用户可在开发模式下通过配置文件或命令行指定渠道（详见开发文档）

### 5. 找不到更新/网络错误怎么办？

- 检查网络连接，确保可访问更新服务器
- 确认当前版本号高于最新 stable 版（或对应渠道）
- 如遇"检查更新失败"，可稍后重试或联系支持

---

## 🐛 故障排除

- **无法检测到新版本**：请确认当前渠道和版本号，或等待新版本发布
- **网络错误/下载失败**：检查网络，或稍后重试
- **强制更新无法跳过**：为保障安全和体验，部分版本必须升级

---

## 📚 相关文档

- [常见问题解答](./faq.md)
- [故障排除](./troubleshooting.md)

---

如有更多问题，欢迎访问 [GitHub Issues](https://github.com/mkdir700/echolab/issues) 或加入社区讨论。
