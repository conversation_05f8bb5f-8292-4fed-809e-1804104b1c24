## [v0.2.0-alpha.6](https://github.com/mkdir700/echolab/tree/v0.2.0-alpha.6)(2025-06-14)

### 🐛 修复问题

- **构建修复**: 发布流程修复，尝试解决打包问题

## [v0.2.0-alpha.5](https://github.com/mkdir700/echolab/tree/v0.2.0-alpha.5)(2025-06-14)

### 🚀 新功能

- **自动更新**: 新增自动更新功能，支持自动检查和下载更新

### 🐛 修复问题

- **构建修复**: 发布流程修复，尝试解决打包问题

## [v0.2.0-alpha.4](https://github.com/mkdir700/echolab/tree/v0.2.0-alpha.4)(2025-06-13)

### 🚀 多平台多架构支持

- **Windows**: 支持 x64 和 arm64 架构
- **macOS**: 支持 x64 和 arm64 架构
- **Linux**: 支持 x64 架构

## [v0.2.0-alpha.3](https://github.com/mkdir700/echolab/tree/v0.2.0-alpha.3)(2025-06-12)

### 🐛 修复问题

- **依赖优化**: 移除 cheerio 依赖以解决 Electron 打包问题，使用原生正则表达式替代 HTML 解析 #50
- **字典解析**: 重构 parseEudicHtml() 为 parseEudicHtmlWithRegex()，支持多种 HTML 格式解析
- **运行时兼容**: 提升 Electron 运行时兼容性，减少打包体积

### ⚙️ 自动化改进

- **发布流程**: 新增自动化发布和版本检查功能，包括 release:auto 和 release:check 命令
- **Git Hook**: 添加 Git pre-push hook，在推送前自动检查版本状态
- **版本管理**: 新增版本管理和发布指南文档，详细说明版本管理流程
- **发布检查**: 实现发布前检查脚本，验证版本号、Git 状态和基本测试

### 🛠️ 构建优化

- **构建目标**: 移除 Linux 构建目标中的 snap 选项，简化构建配置
- **自动化脚本**: 实现自动化发布脚本，支持用户选择版本类型和发布渠道

## [v0.2.0-alpha.2](https://github.com/mkdir700/echolab/tree/v0.2.0-alpha.2)(2025-06-12)

### 🎯 字幕模式增强

- **字幕模式覆盖层**: 新增字幕模式覆盖层组件，支持实时显示当前字幕模式状态
- **快捷键切换**: 完善字幕模式快捷键功能，提供即时的视觉反馈
- **模式监控**: 新增字幕模式监控钩子，自动跟踪字幕模式变化
- **测试覆盖**: 增加字幕覆盖层组件的单元测试，提升代码质量

### 🛠️ 构建和配置优化

- **依赖管理**: 更新核心依赖版本，包括 electron、antd、cheerio 等
- **版本约束**: 在 package.json 中新增 overrides 配置，确保依赖版本一致性
- **构建简化**: 简化 CI/CD 流程，移除不必要的测试和检查步骤
- **多架构支持**: 优化 electron-builder 配置，支持 x64 和 arm64 多架构打包

### 🔧 技术改进

- **类型配置**: 优化 tsconfig.node.json 配置，简化包含路径
- **代码质量**: 修复依赖版本不一致问题，提升项目稳定性
- **构建缓存**: 优化构建缓存机制，提高构建效率

## [v0.2.0-alpha.1](https://github.com/mkdir700/echolab/tree/v0.2.0-alpha.1)(2025-06-11)

### 🎨 全新主题系统

- **统一设计**: 全面重构UI组件，采用统一的主题系统管理样式
- **一致体验**: 所有组件移除CSS Modules，使用标准化设计令牌（spacing、font-weights、border-radius等）
- **主题切换**: 完善的明暗主题支持，提供更加流畅的主题切换体验
- **响应式布局**: 优化组件响应式设计，提升不同屏幕尺寸下的用户体验

### 🖥️ 全屏模式增强

- **沉浸式体验**: 全新的沉浸式全屏播放体验
- **智能控制**: 全屏模式下的专用控制界面，支持进度条、音量控制等功能
- **快捷键优化**: 增强全屏模式下的快捷键支持
- **自适应样式**: 全屏模式下自动切换深色主题，提供最佳视觉效果

### 🎯 字幕功能优化

- **智能分段**: 新增智能字幕分段功能，自动优化字幕显示效果
- **文本选择**: 支持字幕文本选择和快捷复制功能
- **右键菜单**: 新增字幕右键菜单，提供更多操作选项
- **布局锁定**: 字幕位置锁定功能，避免意外拖拽
- **空状态优化**: 改进字幕空状态显示，支持拖拽导入字幕文件

### 🎮 播放控制增强

- **播放速度**: 新增播放速度覆盖层显示，实时反馈速度变化
- **音量控制**: 优化音量控制组件，提供更精确的音量调节
- **进度管理**: 改进播放进度管理，更好的进度保存和恢复
- **视频兼容性**: 新增视频兼容性检测和转码功能

### 🔧 技术架构改进

- **状态管理**: 引入Zustand状态管理，提升性能和维护性
- **组件解耦**: 大规模重构VideoPlayer和SubtitleV3模块，提高代码可维护性
- **样式系统**: 移除CSS Modules依赖，统一使用主题系统管理样式
- **性能优化**: 优化渲染性能，减少不必要的重渲染

### 📁 文件管理优化

- **最近播放**: 优化最近观看记录的加载状态显示
- **视频管理**: 新增视频文件管理组件和确认模态框
- **数据目录**: 新增数据目录管理功能
- **外部链接**: 支持外部链接打开功能

### 📚 文档和测试

- **完整文档**: 新增EchoLab完整文档和用户指南
- **GitHub Pages**: 设置GitHub Pages部署，提供在线文档访问
- **E2E测试**: 新增端到端测试用例，提升代码质量
- **开发指南**: 完善开发者文档和贡献指南

## [v0.1.0-beta.1](https://github.com/mkdir700/echolab/tree/v0.1.0-beta.1)(2025-06-04)

### 🎯 逐句精听系统

- **一键跳转**: 快速跳转到上一句/下一句字幕
- **自动暂停**: 每句结束后自动暂停，便于消化理解
- **单句循环**: 重复播放当前句子，强化练习效果
- **精确同步**: 字幕与视频完美同步显示

### 📽️ 专业播放控制

- **变速播放**: 支持 0.25x - 2.0x 多档速度调节
- **精确跳转**: 10秒前进/后退功能
- **音量控制**: 快捷键调节音量

### 📚 智能字幕系统

- **多格式支持**: 兼容 SRT、VTT、ASS/SSA、JSON 格式
- **自动检测**: 智能识别同名字幕文件
- **双语显示**: 支持原文、译文、双语三种模式
- **自由定位**: 字幕位置可拖拽调整

### 🗂️ 学习管理

- **播放记录**: 自动保存观看进度
- **文件管理**: 智能的最近播放列表
- **进度恢复**: 自动恢复上次播放位置

### 🎮 快捷键支持

| 功能       | 快捷键   | 说明              |
| ---------- | -------- | ----------------- |
| 播放/暂停  | `空格键` | 切换播放状态      |
| 上一句字幕 | `H`      | 跳转到上一句      |
| 下一句字幕 | `L`      | 跳转到下一句      |
| 单句循环   | `R`      | 开启/关闭循环     |
| 自动暂停   | `P`      | 开启/关闭自动暂停 |
| 音量调节   | `↑/↓`    | 调大/调小音量     |
| 快速跳转   | `←/→`    | 后退/前进10秒     |

### 📁 支持格式

- **常见格式**: MP4, AVI, MKV, MOV, WMV, FLV
- **高清支持**: 4K/1080P 高清视频
- **编码兼容**: 支持主流视频编码格式

### 字幕格式

- **SRT**: 最常用的字幕格式
- **VTT**: Web 标准字幕格式
- **ASS/SSA**: 高级样式字幕格式
- **JSON**: 自定义 JSON 字幕格式
