<common_patterns>
  <pattern name="layout_component">
    <usage>用于创建灵活的布局容器，如 HStack 和 VStack。</usage>
    <template>
    <![CDATA[
import styled from 'styled-components';

export const HStack = styled.div<{
  gap?: string;
  alignItems?: string;
  justifyContent?: string;
}>`
  display: flex;
  flex-direction: row;
  gap: ${(props) => props.gap || '0'};
  align-items: ${(props) => props.alignItems || 'stretch'};
  justify-content: ${(props) => props.justifyContent || 'flex-start'};
`;
    ]]>
    </template>
  </pattern>
  <pattern name="themed_component">
    <usage>创建一个能响应主题变化的基本组件。</usage>
    <template>
    <![CDATA[
import styled from 'styled-components';

const StyledButton = styled.button`
  background-color: var(--primary-color);
  color: var(--text-on-primary);
  border: 1px solid var(--primary-color);
  padding: 8px 16px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--primary-color-hover);
  }
`;

export function Button({ children, ...props }) {
  return <StyledButton {...props}>{children}</StyledButton>;
}
    ]]>
    </template>
  </pattern>
</common_patterns>
