<examples>
  <example name="create_themed_button_from_figma">
    <description>此示例演示了如何从 Figma 设计稿中获取设计规范，并创建一个新的、支持主题切换的 React 按钮组件。</description>
    <context>当设计团队在 Figma 中提供了一个新的按钮设计，需要开发人员在代码库中实现它时使用。</context>
    <code language="typescript">
    <![CDATA[
// 1. 从 Figma 获取设计规范 (手动或通过工具)
//    - Primary Color: #007bff
//    - Hover Color: #0056b3
//    - Text Color: #ffffff
//    - Border Radius: 4px
//    - Padding: 8px 16px

// 2. 将规范转换为设计令牌 (如果尚不存在)
//    :root {
//      --button-primary-background: #007bff;
//      --button-primary-background-hover: #0056b3;
//      --button-primary-text: #ffffff;
//      --button-border-radius: 4px;
//      --button-padding: 8px 16px;
//    }

// 3. 创建组件文件 src/renderer/src/components/ThemedButton/ThemedButton.tsx
import styled from 'styled-components';
import React from 'react';

const StyledButton = styled.button`
  background-color: var(--button-primary-background);
  color: var(--button-primary-text);
  border: none;
  padding: var(--button-padding);
  border-radius: var(--button-border-radius);
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--button-primary-background-hover);
  }
`;

interface ThemedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
}

export function ThemedButton({ children, ...props }: ThemedButtonProps) {
  return <StyledButton {...props}>{children}</StyledButton>;
}
    ]]>
    </code>
    <explanation>
      此示例首先将设计规范转换为可维护的设计令牌。然后，使用 `styled-components` 创建一个 React 组件，该组件的样式完全由这些设计令牌驱动。这确保了按钮的外观可以在整个应用程序中通过更新 CSS 变量来轻松更改，从而实现了强大的主题化能力。
    </explanation>
  </example>
</examples>
