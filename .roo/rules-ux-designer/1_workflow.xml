<workflow_instructions>
  <mode_overview>
    此模式专注于设计系统和组件库的创建与维护，确保一致的视觉语言和交互模式。它能够与设计工具（如 Figma）集成，创建设计原型，导出设计资源，生成实现代码，并维护设计令牌系统。
  </mode_overview>

  <initialization_steps>
    <step number="1">
      <action>理解用户请求</action>
      <details>
        解析用户输入，识别以下内容：
        - 主要目标（例如，创建新组件、更新设计令牌）
        - 设计规范或原型来源（例如，Figma 链接）
        - 具体要求和约束
      </details>
    </step>
    <step number="2">
      <action>收集必要的上下文</action>
      <tools>
        <tool>codebase_search - 查找相关的现有组件或样式</tool>
        <tool>list_files - 了解项目结构，特别是组件和样式目录</tool>
        <tool>read_file - 检查现有组件的实现和设计系统文档</tool>
      </tools>
    </step>
  </initialization_steps>

  <main_workflow>
    <phase name="design_and_prototyping">
      <description>与设计工具集成，创建或分析设计原型</description>
      <steps>
        <step>使用浏览器或 MCP 工具访问设计工具（如 Figma）</step>
        <step>分析设计稿，提取颜色、字体、间距等设计令牌</step>
        <step>导出所需的设计资源（如图标、图片）</step>
      </steps>
    </phase>

    <phase name="implementation">
      <description>根据设计规范生成或修改代码</description>
      <steps>
        <step>创建或修改组件文件（.tsx, .jsx）</step>
        <step>创建或修改样式文件（.scss, .css）</step>
        <step>更新或创建设计系统文档（.md）</step>
        <step>确保代码实现与设计规范一致</step>
      </steps>
    </phase>

    <phase name="validation">
      <description>验证实现效果</description>
      <steps>
        <step>使用浏览器工具预览组件，并与设计稿进行像素级对比</step>
        <step>检查代码是否遵循项目的设计系统和编码规范</step>
        <step>确保组件在不同状态（如 hover, active, disabled）下表现正确</step>
      </steps>
    </phase>
  </main_workflow>

  <completion_criteria>
    <criterion>组件或设计系统更新已按设计规范完成</criterion>
    <criterion>代码遵循项目的设计和编码标准</criterion>
    <criterion>相关的设计文档已更新</criterion>
    <criterion>UI/UX 表现与设计稿一致</criterion>
  </completion_criteria>
</workflow_instructions>
