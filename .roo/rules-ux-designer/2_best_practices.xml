<best_practices>
  <general_principles>
    <principle priority="high">
      <name>一致性原则</name>
      <description>在整个应用程序中保持视觉和交互的一致性。组件、颜色、字体和布局应遵循统一的模式。</description>
      <rationale>一致性可以降低用户的学习成本，建立用户的信任感，并提高可用性。</rationale>
    </principle>
    <principle priority="high">
      <name>设计令牌驱动</name>
      <description>所有样式（颜色、字体、间距等）都应通过设计令牌（CSS 变量）来管理，而不是在代码中硬编码。这确保了主题和样式可以被动态和一致地更新。</description>
      <rationale>使主题切换、品牌更新和全局样式调整变得简单可靠。</rationale>
      <example>
        <scenario>设置组件的边框颜色</scenario>
        <good>border: 2px solid var(--color-border);</good>
        <bad>border: 2px solid #e0e0e0;</bad>
      </example>
    </principle>
  </general_principles>

  <code_conventions>
    <convention category="styling">
      <rule>使用 `styled-components` 为组件定义样式。将样式定义与组件逻辑放在同一个文件中，或者在组件目录中创建一个单独的 `styles.ts` 文件。</rule>
      <rationale>这有助于将样式与组件逻辑紧密耦合，提高组件的可移植性和可维护性。</rationale>
      <template>
      <![CDATA[
import styled from 'styled-components';

const ComponentWrapper = styled.div`
  background-color: var(--background-color);
  color: var(--text-color);
`;

export function MyComponent() {
  return <ComponentWrapper>...</ComponentWrapper>;
}
      ]]>
      </template>
    </convention>
    <convention category="component_structure">
      <rule>将相关的自定义 hooks、上下文和辅助函数与使用它们的组件放在相近的目录结构中，以提高内聚性。</rule>
      <example>
        `useUserTheme` hook 用于管理用户主题，并被 `AppearanceSettings` 组件使用。
      </example>
    </convention>
  </code_conventions>

  <common_pitfalls>
    <pitfall>
      <description>在 `styled-components` 中硬编码样式值，而不是使用设计令牌。</description>
      <why_problematic>这会破坏主题系统，使组件在主题切换时无法正确更新样式。</why_problematic>
      <correct_approach>始终通过 `var(--token-name)` 的形式引用设计令牌。</correct_approach>
    </pitfall>
  </common_pitfalls>

  <quality_checklist>
    <category name="component_design">
      <item>组件是否完全通过 `styled-components` 和设计令牌来设置样式？</item>
      <item>组件是否能正确响应主题切换？</item>
      <item>组件的 props 设计是否灵活，易于扩展？</item>
    </category>
    <category name="design_system_maintenance">
      <item>新的颜色或样式是否已添加为设计令牌？</item>
      <item>组件是否在设计系统文档中有清晰的说明和示例？</item>
    </category>
  </quality_checklist>
</best_practices>