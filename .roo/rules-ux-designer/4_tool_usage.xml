<tool_usage_guide>
  <tool_priorities>
    <priority level="1">
      <tool>browser_navigate</tool>
      <when>当需要访问在线设计工具（如 Figma）时</when>
      <why>这是与基于 Web 的设计工具进行交互的第一步。</why>
    </priority>
    <priority level="2">
      <tool>browser_snapshot</tool>
      <when>在设计工具页面加载后</when>
      <why>获取页面的可访问性快照，以便识别和操作设计元素。</why>
    </priority>
  </tool_priorities>

  <tool_specific_guidance>
    <tool name="browser_action (generic)">
      <best_practices>
        <practice>结合使用 `browser_navigate`, `browser_snapshot`, 和 `browser_click` 来与设计工具进行交互。</practice>
        <practice>使用 `browser_screenshot` 来捕获设计稿的特定部分，以便进行像素级比较。</practice>
      </best_practices>
    </tool>
    <tool name="mcp (generic)">
      <best_practices>
        <practice>如果配置了 Figma 或其他设计工具的 MCP 服务器，优先使用其提供的工具来提取设计令牌和资源。</practice>
        <practice>使用 MCP 工具可以更可靠、更结构化地获取设计数据，而不是通过屏幕抓取。</practice>
      </best_practices>
    </tool>
  </tool_specific_guidance>

  <workflow_example>
    <scenario>从 Figma 设计稿生成一个 React 组件</scenario>
    <steps>
      <step number="1">使用 `browser_navigate` 打开 Figma 链接。</step>
      <step number="2">使用 `browser_snapshot` 获取页面信息，并使用 `browser_click` 或 `browser_hover` 与设计元素交互，以查看其属性（颜色、字体、间距等）。</step>
      <step number="3">使用 `write_to_file` 创建一个新的组件文件（例如 `src/renderer/src/components/NewComponent/NewComponent.tsx`）。</step>
      <step number="4">在组件文件中，使用 `styled-components` 和从 Figma 中提取的设计令牌（CSS 变量）来实现样式。</step>
      <step number="5">使用 `browser_action` 和本地开发服务器来预览新组件，并使用 `browser_screenshot` 与原始设计稿进行比较。</step>
    </steps>
  </workflow_example>
</tool_usage_guide>
