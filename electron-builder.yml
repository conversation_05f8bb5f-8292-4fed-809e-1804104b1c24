appId: vip.echolab.app
productName: echolab
directories:
  buildResources: build
icon: icons
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintcache,eslint.config.mjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
asarUnpack:
  - resources/**
asar: true
copyright: Copyright © 2025 EchoLab
win:
  executableName: echolab
  icon: icons/win/icon.ico
  artifactName: ${productName}-${version}-${arch}.${ext}
  target:
    - target: nsis
      arch:
        - x64
        - arm64
nsis:
  oneClick: false
  perMachine: false
  allowToChangeInstallationDirectory: true
  deleteAppDataOnUninstall: true
  artifactName: ${productName}-${version}-${arch}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
mac:
  entitlementsInherit: build/entitlements.mac.plist
  icon: icons/mac/icon.icns
  artifactName: ${productName}-${version}-${arch}.${ext}
  target:
    - target: dmg
      arch:
        - x64
        - arm64
    - target: zip
      arch:
        - x64
        - arm64
  category: public.app-category.education
  darkModeSupport: true
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${productName}-${version}-${arch}.${ext}
linux:
  target:
    - target: AppImage
      arch:
        - x64
        - arm64
    - target: deb
      arch:
        - x64
        - arm64
  maintainer: mkdir700
  category: Education
  icon: icons/png
  artifactName: ${productName}-${version}-${arch}.${ext}
appImage:
  artifactName: ${productName}-${version}-${arch}.${ext}
deb:
  artifactName: ${productName}-${version}-${arch}.${ext}
npmRebuild: false
publish:
  provider: github
  owner: mkdir700
  repo: echolab
  vPrefixedTagName: true
  releaseType: draft
  publishAutoUpdate: true
generateUpdatesFilesForAllChannels: true
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
