name: Build and Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0, v1.0.0-beta.1, v1.0.0-alpha.1)'
        required: false
      force_version_type:
        description: 'Force version type (overrides auto-detection)'
        required: false
        type: choice
        options:
          - 'dev'
          - 'test'
          - 'alpha'
          - 'beta'
          - 'stable'

permissions:
  contents: write
  pull-requests: read

jobs:
  detect-version:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      version_type: ${{ steps.version.outputs.version_type }}
      is_prerelease: ${{ steps.version.outputs.is_prerelease }}
      upload_path: ${{ steps.version.outputs.upload_path }}
      autoupdate_path: ${{ steps.version.outputs.autoupdate_path }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Detect version and type
        id: version
        shell: bash
        run: |
          # Get version from input, tag, or package.json
          if [ -n "${{ github.event.inputs.version }}" ]; then
            VERSION="${{ github.event.inputs.version }}"
          elif [ -n "${{ github.ref_name }}" ] && [[ "${{ github.ref_name }}" == v* ]]; then
            VERSION="${{ github.ref_name }}"
          else
            VERSION="v$(node -p "require('./package.json').version")"
          fi

          # Remove 'v' prefix for processing
          VERSION_NO_V="${VERSION#v}"

          # Detect version type
          if [ -n "${{ github.event.inputs.force_version_type }}" ]; then
            VERSION_TYPE="${{ github.event.inputs.force_version_type }}"
          elif [[ "$VERSION_NO_V" == *"-dev"* ]]; then
            VERSION_TYPE="dev"
          elif [[ "$VERSION_NO_V" == *"-test"* ]]; then
            VERSION_TYPE="test"
          elif [[ "$VERSION_NO_V" == *"-alpha"* ]]; then
            VERSION_TYPE="alpha"
          elif [[ "$VERSION_NO_V" == *"-beta"* ]]; then
            VERSION_TYPE="beta"
          else
            VERSION_TYPE="stable"
          fi

          # Determine if it's a prerelease
          if [[ "$VERSION_TYPE" != "stable" ]]; then
            IS_PRERELEASE="true"
          else
            IS_PRERELEASE="false"
          fi

          # Set upload paths based on version type
          case "$VERSION_TYPE" in
            "dev"|"test")
              UPLOAD_PATH="/test-releases/"
              AUTOUPDATE_PATH="/test-autoupdate/"
              ;;
            "alpha"|"beta")
              UPLOAD_PATH="/prerelease/"
              AUTOUPDATE_PATH="/prerelease-autoupdate/"
              ;;
            "stable")
              UPLOAD_PATH="/releases/"
              AUTOUPDATE_PATH="/autoupdate/"
              ;;
          esac

          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "version_type=$VERSION_TYPE" >> $GITHUB_OUTPUT
          echo "is_prerelease=$IS_PRERELEASE" >> $GITHUB_OUTPUT
          echo "upload_path=$UPLOAD_PATH" >> $GITHUB_OUTPUT
          echo "autoupdate_path=$AUTOUPDATE_PATH" >> $GITHUB_OUTPUT

          echo "🏷️ Version: $VERSION"
          echo "📦 Version Type: $VERSION_TYPE"
          echo "🚀 Is Prerelease: $IS_PRERELEASE"
          echo "📁 Upload Path: $UPLOAD_PATH"
          echo "🔄 AutoUpdate Path: $AUTOUPDATE_PATH"

  release:
    needs: detect-version
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          # Windows builds - 支持 x64 和 ARM64 架构
          - os: windows-latest
            platform: win
            target: --win
          # macOS builds - 支持 Intel 和 Apple Silicon 架构
          - os: macos-latest
            platform: mac
            target: --mac
          # Linux builds - 支持 x64 和 ARM64 架构
          - os: ubuntu-latest
            platform: linux
            target: --linux

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 8

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Build for ${{ matrix.platform }}
        run: |
          echo "🏗️ Building for ${{ matrix.platform }}"
          echo "构建平台: ${{ matrix.platform }}"
          pnpm build
          pnpm exec electron-builder ${{ matrix.target }} --publish never
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: List build artifacts
        shell: bash
        run: |
          echo "📦 Build artifacts in dist directory:"
          if [ -d "dist" ]; then
            if command -v ls >/dev/null 2>&1; then
              ls -la dist/
            else
              # Windows fallback
              dir dist /a
            fi
          else
            echo "No dist directory found"
          fi
          echo "📁 Current directory contents:"
          if command -v ls >/dev/null 2>&1; then
            ls -la
          else
            # Windows fallback
            dir /a
          fi

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.platform }}-artifacts
          path: |
            dist/*.exe
            dist/*.dmg
            dist/*.zip
            dist/*.AppImage
            dist/*.deb
            dist/latest*.yml
            dist/latest*.yaml
            dist/*.blockmap
          retention-days: 30
          if-no-files-found: warn

      - name: Create Release and Upload Assets
        if: startsWith(github.ref, 'refs/tags/v')
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ needs.detect-version.outputs.version }}
          name: ${{ needs.detect-version.outputs.version }}
          draft: true
          prerelease: ${{ needs.detect-version.outputs.is_prerelease }}
          files: |
            dist/*.exe
            dist/*.dmg
            dist/*.zip
            dist/*.AppImage
            dist/*.deb
            dist/latest*.yml
            dist/latest*.yaml
            dist/*.blockmap
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  notify:
    needs: [detect-version, release]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Build Status Summary
        run: |
          echo "🏗️ Build Summary"
          echo "==============="
          echo "Version: ${{ needs.detect-version.outputs.version }}"
          echo "Type: ${{ needs.detect-version.outputs.version_type }}"
          echo "Prerelease: ${{ needs.detect-version.outputs.is_prerelease }}"
          echo "Status: ${{ job.status }}"

          if [ "${{ needs.detect-version.outputs.version_type }}" == "beta" ]; then
            echo ""
            echo "🧪 Beta Release Notes:"
            echo "- This is a beta version, please test thoroughly"
            echo "- Report any issues on GitHub"
            echo "- Auto-update is enabled for beta users"
          fi
