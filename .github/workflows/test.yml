name: Test

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        node-version: [18, 20]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 8

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Run linter
        run: pnpm lint

      - name: Type check
        run: pnpm typecheck

      - name: Run tests
        run: pnpm test:coverage

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}

      - name: Test build
        run: pnpm build

      - name: Install Playwright
        run: pnpm exec playwright install --with-deps chromium
        if: matrix.os == 'ubuntu-latest' && matrix.node-version == '20'

      - name: Setup virtual display for E2E tests
        run: |
          sudo apt-get update
          sudo apt-get install -y xvfb
          export DISPLAY=:99
          Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
        if: matrix.os == 'ubuntu-latest' && matrix.node-version == '20'

      - name: Run E2E tests (Ubuntu only)
        run: |
          export DISPLAY=:99
          pnpm test:e2e
        if: matrix.os == 'ubuntu-latest' && matrix.node-version == '20'

      - name: Upload E2E test results
        uses: actions/upload-artifact@v4
        if: always() && matrix.os == 'ubuntu-latest' && matrix.node-version == '20'
        with:
          name: e2e-report
          path: |
            e2e-report/
            e2e-results/
          retention-days: 7
