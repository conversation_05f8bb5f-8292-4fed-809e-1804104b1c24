# 开发模式下的更新配置 / Development mode update configuration
# 这个文件用于在开发环境中测试更新功能，无需实际打包应用
# This file is used to test update functionality in development environment without actual packaging

# 使用本地服务器进行测试 / Use local server for testing
provider: generic
url: http://localhost:8384
channel: latest
# 备用配置：使用 GitHub 进行测试 / Alternative config: use GitHub for testing
# provider: github
# repo: echolab
# owner: mkdir700
# updaterCacheDirName: echolab-updater
