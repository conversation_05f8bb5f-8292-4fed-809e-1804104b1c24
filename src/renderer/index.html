<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>EchoLab</title>
    <!-- https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP -->
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self'; connect-src blob: *; script-src 'self' 'unsafe-eval' 'unsafe-inline' *; worker-src 'self' blob:; style-src 'self' 'unsafe-inline' *; font-src 'self' data: *; img-src 'self' data: file: * blob:; frame-src * file:" />
  </head>

  <body>
    <div id="root"></div>
    <script src="http://localhost:8097"></script>
    <script type="module" src="/src/init.ts"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
