/**
 * HomePage 业务管理器 / HomePage Business Manager
 *
 * 管理HomePage相关的业务逻辑，协调服务层与状态层的交互
 * Manages HomePage related business logic, coordinates interaction between service layer and state layer
 */

import { preferencesSelectors, usePreferencesStore } from '@renderer/state/stores/preferences.store'
import { logger } from '@renderer/utils/logger'
import { UserPreferences } from '@types_/domain'

import {
  type FileSelectionOptions,
  type FileSelectionResult,
  FileSelectionService
} from '../../services/api'
import {
  type RecentPlayItem,
  type RecentPlaysQueryParams,
  RecentPlaysService
} from '../../services/storage'
import { HomePageStore, useHomePageStore } from '../../state/stores/home.store'

export class HomePageServiceError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly originalError?: Error
  ) {
    super(message)
    this.name = 'HomePageManagerError'
  }
}

/**
 * 最近播放项目加载结果 / Recent Play Items Load Result
 */
export interface RecentPlayItemsLoadResult {
  /** 是否成功 / Whether successful */
  success: boolean

  /** 播放项目列表 / Play items list */
  items: RecentPlayItem[]

  /** 总数量 / Total count */
  totalCount: number

  /** 是否有更多数据 / Whether has more data */
  hasMore: boolean

  /** 错误信息 / Error message */
  error?: string
}

/**
 * 文件选择结果扩展 / Extended File Selection Result
 */
export interface ExtendedFileSelectionResult extends FileSelectionResult {
  /** 是否已添加到最近播放 / Whether added to recent plays */
  addedToRecentPlays: boolean

  /** 最近播放项目ID / Recent play item ID */
  recentPlayItemId?: string
}

/**
 * HomePage 服务类 / HomePage Service Class
 */
export class HomePageService {
  private readonly fileSelectionService: FileSelectionService
  private readonly recentPlaysService: RecentPlaysService
  private readonly homePageStore = useHomePageStore

  constructor() {
    this.fileSelectionService = new FileSelectionService()
    this.recentPlaysService = new RecentPlaysService()
  }

  /**
   * 加载最近播放项目 / Load recent play items
   */
  async loadRecentPlayItems(
    params: RecentPlaysQueryParams = {}
  ): Promise<RecentPlayItemsLoadResult> {
    try {
      logger.info('📋 正在加载最近播放项目...', params)

      // 设置加载状态 / Set loading state
      this.homePageStore.getState().setVideoCardLoading(true)
      this.homePageStore.getState().clearError()

      // 获取数据 / Get data
      const items = await this.recentPlaysService.getRecentPlays(params)
      const totalCount = items.length

      // 计算是否有更多数据 / Calculate if has more data
      const { limit = 20, offset = 0 } = params
      const hasMore = totalCount > offset + limit

      const result: RecentPlayItemsLoadResult = {
        success: true,
        items,
        totalCount,
        hasMore
      }

      logger.info(`✅ 成功加载 ${items.length} 个最近播放项目`)
      return result
    } catch (error) {
      const errorMessage = `Failed to load recent play items: ${error instanceof Error ? error.message : 'Unknown error'}`
      logger.error('❌ 加载最近播放项目失败:', error)

      // 设置错误状态 / Set error state
      this.homePageStore.getState().setError(errorMessage)

      return {
        success: false,
        items: [],
        totalCount: 0,
        hasMore: false,
        error: errorMessage
      }
    } finally {
      // 清除加载状态 / Clear loading state
      this.homePageStore.getState().setVideoCardLoading(false)
    }
  }

  /**
   * 选择并添加视频文件 / Select and add video file
   */
  async selectAndAddVideoFile(
    options: FileSelectionOptions = {}
  ): Promise<ExtendedFileSelectionResult> {
    try {
      logger.info('🎬 开始选择视频文件...')

      // 设置文件选择状态 / Set file selection state
      this.homePageStore.getState().startFileSelection()

      // 选择文件 / Select file
      const selectionResult = await this.fileSelectionService.selectVideoFile(options)

      if (!selectionResult.success || !selectionResult.videoFile) {
        logger.info('📋 视频文件选择取消或失败')
        return {
          ...selectionResult,
          addedToRecentPlays: false
        }
      }

      // 添加到最近播放记录 / Add to recent plays
      let addedToRecentPlays = false
      let recentPlayItemId: string | undefined

      try {
        const recentPlayItem = await this.recentPlaysService.addOrUpdatePlay({
          filePath: selectionResult.videoFile.filePath,
          fileName: selectionResult.videoFile.fileName,
          duration: selectionResult.videoFile.duration || 0,
          currentTime: 0,
          thumbnailPath: selectionResult.videoFile.thumbnailPath,
          subtitlePath: selectionResult.subtitleFiles?.[0]?.filePath,
          fileSize: selectionResult.videoFile.fileSize,
          isFavorite: false
        })

        addedToRecentPlays = true
        recentPlayItemId = recentPlayItem.id
        logger.info('✅ 视频文件已添加到最近播放记录')
      } catch (error) {
        logger.warn('⚠️ 添加到最近播放记录失败:', error)
        // 不影响主要功能 / Don't affect main functionality
      }

      const result: ExtendedFileSelectionResult = {
        ...selectionResult,
        addedToRecentPlays,
        recentPlayItemId
      }

      logger.info('✅ 视频文件选择完成')
      return result
    } catch (error) {
      const errorMessage = `视频文件选择失败: ${error instanceof Error ? error.message : 'Unknown error'}`
      logger.error('❌ 视频文件选择失败:', error)

      // 设置错误状态 / Set error state
      this.homePageStore.getState().setError(errorMessage)

      return {
        success: false,
        error: errorMessage,
        addedToRecentPlays: false
      }
    } finally {
      // 完成文件选择 / Finish file selection
      this.homePageStore.getState().finishFileSelection()
    }
  }

  /**
   * 删除最近播放项目 / Delete recent play item
   */
  async deleteRecentPlayItem(itemId: string): Promise<boolean> {
    try {
      logger.info(`🗑️ 正在删除最近播放项目: ${itemId}`)

      const success = await this.recentPlaysService.deletePlay(itemId)

      if (success) {
        // 关闭模态框 / Close modal
        this.homePageStore.getState().closeDeleteModal()
        this.homePageStore.getState().hideDeleteConfirm()

        logger.info('✅ 最近播放项目删除成功')
      } else {
        throw new HomePageServiceError('删除最近播放项目失败', 'DELETE_FAILED')
      }

      return success
    } catch (error) {
      const errorMessage = `删除最近播放项目失败: ${error instanceof Error ? error.message : 'Unknown error'}`
      logger.error('❌ 删除最近播放项目失败:', error)

      // 设置错误状态 / Set error state
      this.homePageStore.getState().setError(errorMessage)

      return false
    }
  }

  /**
   * 清空所有最近播放记录 / Clear all recent play records
   */
  async clearAllRecentPlays(): Promise<boolean> {
    try {
      logger.info('🧹 正在清空所有最近播放记录...')

      await this.recentPlaysService.clearAllPlays()

      // 关闭模态框 / Close modal
      this.homePageStore.getState().closeClearModal()

      logger.info('✅ 所有最近播放记录已清空')
      return true
    } catch (error) {
      const errorMessage = `清空最近播放记录失败: ${error instanceof Error ? error.message : 'Unknown error'}`
      logger.error('❌ 清空最近播放记录失败:', error)

      // 设置错误状态 / Set error state
      this.homePageStore.getState().setError(errorMessage)

      return false
    }
  }

  /**
   * 切换收藏状态 / Toggle favorite status
   */
  async toggleFavoriteStatus(itemId: string): Promise<boolean> {
    try {
      logger.info(`⭐ 正在切换收藏状态: ${itemId}`)

      const newStatus = await this.recentPlaysService.toggleFavorite(itemId)

      logger.info(`✅ 收藏状态已更新为: ${newStatus ? '收藏' : '取消收藏'}`)
      return newStatus
    } catch (error) {
      const errorMessage = `切换收藏状态失败: ${error instanceof Error ? error.message : 'Unknown error'}`
      logger.error('❌ 切换收藏状态失败:', error)

      // 设置错误状态 / Set error state
      this.homePageStore.getState().setError(errorMessage)

      return false
    }
  }

  /**
   * 加载用户偏好设置 / Load user preferences
   */
  async loadUserPreferences(): Promise<UserPreferences | null> {
    try {
      logger.info('⚙️ 正在加载用户偏好设置...')

      const preferences = preferencesSelectors.all(usePreferencesStore.getState())

      // 应用主题设置到状态 / Apply theme settings to state
      // this.homePageStore
      //   .getState()
      //   .setThemeMode(preferences.theme.compactMode ? 'compact' : 'default')

      logger.info('✅ 用户偏好设置加载成功')
      return preferences
    } catch (error) {
      logger.error('❌ 加载用户偏好设置失败:', error)
      return null
    }
  }

  /**
   * 更新主题设置 / Update theme settings
   */
  async updateThemeMode(compactMode: boolean): Promise<boolean> {
    try {
      logger.info(`🎨 正在更新主题模式: ${compactMode ? '紧凑模式' : '默认模式'}`)

      // 保存到用户偏好 / Save to user preferences
      usePreferencesStore.getState().updatePreference('theme', { compactMode })

      logger.info('✅ 主题设置更新成功')
      return true
    } catch (error) {
      logger.error('❌ 更新主题设置失败:', error)

      // 设置错误状态 / Set error state
      this.homePageStore
        .getState()
        .setError(`更新主题设置失败: ${error instanceof Error ? error.message : 'Unknown error'}`)

      return false
    }
  }

  /**
   * 打开删除确认对话框 / Open delete confirmation dialog
   */
  openDeleteConfirmDialog(itemId: string, fileName: string): void {
    logger.info(`🗑️ 打开删除确认对话框: ${fileName}`)
    this.homePageStore.getState().showDeleteConfirm(itemId, fileName)
    this.homePageStore.getState().openDeleteModal()
  }

  /**
   * 关闭删除确认对话框 / Close delete confirmation dialog
   */
  closeDeleteConfirmDialog(): void {
    logger.info('❌ 关闭删除确认对话框')
    this.homePageStore.getState().hideDeleteConfirm()
    this.homePageStore.getState().closeDeleteModal()
  }

  /**
   * 打开清空确认对话框 / Open clear confirmation dialog
   */
  openClearConfirmDialog(): void {
    logger.info('🧹 打开清空确认对话框')
    this.homePageStore.getState().openClearModal()
  }

  /**
   * 关闭清空确认对话框 / Close clear confirmation dialog
   */
  closeClearConfirmDialog(): void {
    logger.info('❌ 关闭清空确认对话框')
    this.homePageStore.getState().closeClearModal()
  }

  /**
   * 获取当前状态 / Get current state
   */
  getCurrentState(): HomePageStore {
    return this.homePageStore.getState()
  }

  /**
   * 重置错误状态 / Reset error state
   */
  resetError(): void {
    this.homePageStore.getState().clearError()
  }

  /**
   * 验证状态完整性 / Validate state integrity
   */
  validateState(): { isValid: boolean; errors: string[] } {
    return this.homePageStore.getState().validateState()
  }

  /**
   * 重置到默认状态 / Reset to default state
   */
  resetToDefaults(): void {
    this.homePageStore.getState().resetToDefaults()
  }
}

/**
 * 创建 HomePageService 实例的工厂函数 / Factory function to create HomePageService instance
 */
export const createHomePageService = (): HomePageService => {
  return new HomePageService()
}
