/**
 * V2 架构交互 Hooks 导出
 * V2 Architecture Interaction Hooks Exports
 *
 * 提供所有交互相关的React Hook，包括：
 * Provides all interaction-related React Hooks including:
 * - 全屏模式管理 / Fullscreen mode management
 * - 速度覆盖层 / Speed overlay
 * - 遮罩框交互 / Mask frame interaction
 */

// 全屏模式 Hooks / Fullscreen Mode Hooks
// export { useFullscreenMode } from './useFullscreenMode'

// 速度覆盖层 Hooks / Speed Overlay Hooks
// export { useSpeedOverlay } from './useSpeedOverlay'

// 遮罩框交互 Hooks / Mask Frame Interaction Hooks
// export { useMaskFrame } from './useMaskFrame'
