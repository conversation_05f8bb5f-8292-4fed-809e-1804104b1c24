import { FileMetadata, PlaybackHistory } from '@types'
import { Dexie, type EntityTable } from 'dexie'

export const db = new Dexie('EchoLab') as <PERSON><PERSON> & {
  files: EntityTable<FileMetadata, 'id'>
  playbackHistory: EntityTable<PlaybackHistory, 'id'>
  // settings: EntityTable<{ id: string; value: any }, 'id'>
}

db.version(1).stores({
  files: 'id, name, origin_name, path, size, ext, type, created_at',
  playbackRecord: '++id, fileId, currentTime, duration, playedAt, isFinished, [fileId+playedAt]'
})

export default db
