/* SidebarSection 现代化沉浸式样式 */

.sidebarContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: var(--darker-bg);
  /* 移除所有卡片样式 */
  border: none;
  border-radius: 0;
  box-shadow: none;
  backdrop-filter: none;
  /* 移除内边距，让内容完全填充 */
  padding: 0;
  margin: 0;
  /* 确保内容区域完全填充 */
  overflow: hidden;
  position: relative;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sidebarContainer {
    height: 100%;
    max-height: none;
  }
}

@media (max-width: 768px) {
  .sidebarContainer {
    height: 100%;
  }
}
