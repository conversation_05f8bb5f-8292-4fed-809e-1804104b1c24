/* UserInputContainer 组件样式 */

.userInputContainer {
  flex: 1;
  background: var(--card-bg);
  border-radius: var(--border-radius);
  border: 1px solid var(--card-border);
  backdrop-filter: blur(20px);
  display: flex;
  flex-direction: column;
}

.inputHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-sm);
  border-bottom: 1px solid var(--card-border);
}

.userInput {
  background: transparent !important;
  border: none !important;
  color: var(--text-primary) !important;
  resize: none !important;
  padding: var(--spacing-lg) !important;
  font-size: var(--font-size-base) !important;
  line-height: var(--line-height-relaxed) !important;
}

.userInput:focus {
  box-shadow: none !important;
  border: none !important;
}

.userInput::placeholder {
  color: var(--text-muted) !important;
}

/* 快捷键提示 */
.shortcutsHint {
  position: absolute;
  bottom: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  backdrop-filter: blur(20px);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  opacity: 0.8;
}
