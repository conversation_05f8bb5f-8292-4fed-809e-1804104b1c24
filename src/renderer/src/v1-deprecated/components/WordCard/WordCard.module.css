/* WordCard 组件样式 */

.wordCard {
  animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 16px 48px rgba(0, 0, 0, 0.4));
  position: relative;
  z-index: 9999; /* 确保单词卡片在最顶层 */
  pointer-events: auto; /* 确保卡片可以接收点击事件 */
}

.cardContent {
  background: rgba(20, 24, 35, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  border-radius: 12px !important;
  box-shadow:
    0 16px 40px rgba(0, 0, 0, 0.4),
    0 6px 16px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(24px) saturate(150%) !important;
  max-width: 280px;
  min-width: 240px;
  position: relative;
  overflow: visible !important;
  z-index: 9999; /* 确保卡片内容在最顶层 */
  pointer-events: auto; /* 确保卡片内容可以接收点击事件 */
}

/* 指向目标单词的小角 */
.arrow {
  position: absolute;
  bottom: -8px;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid rgba(20, 24, 35, 0.95);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  z-index: 9998; /* 小箭头在卡片下方但仍在最顶层 */
}

.arrow::before {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 9px solid transparent;
  border-right: 9px solid transparent;
  border-top: 9px solid rgba(255, 255, 255, 0.15);
  z-index: 1000;
}

.cardContent :global(.ant-card-head) {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.03) 100%
  ) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 12px 12px 0 0 !important;
  padding: 12px 16px !important;
  min-height: auto !important;
}

.cardContent :global(.ant-card-body) {
  padding: 16px !important;
  background: transparent !important;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  gap: 8px;
}

.wordTitle {
  color: rgba(255, 255, 255, 0.95) !important;
  margin: 0 !important;
  font-size: 18px !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.phonetic {
  color: rgba(102, 126, 234, 0.9) !important;
  font-size: 14px !important;
  font-style: italic !important;
  margin-left: 6px !important;
  font-weight: 500 !important;
}

.playButton {
  color: rgba(102, 126, 234, 0.9) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 8px !important;
}

.playButton:hover {
  color: rgba(102, 126, 234, 1) !important;
  background-color: rgba(102, 126, 234, 0.15) !important;
  transform: scale(1.1) translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.closeButton {
  color: rgba(255, 255, 255, 0.6) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 8px !important;
}

.closeButton:hover {
  color: rgba(255, 255, 255, 0.9) !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  transform: scale(1.1) translateY(-1px);
}

.cardBody {
  max-height: 160px;
  overflow-y: auto;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px 0;
}

.loadingText {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 14px !important;
}

.definitionsContainer {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.translationsSection,
.definitionsSection {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.sectionTitle {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 14px !important;
  font-weight: 700 !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px !important;
}

.translationText {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 15px !important;
  line-height: 1.5 !important;
  font-weight: 500 !important;
}

.divider {
  margin: 12px 0 !important;
  border-color: rgba(255, 255, 255, 0.15) !important;
}

.definitionsList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.definitionItem {
  display: flex;
  gap: 3px;
  padding: 6px 10px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  border-left: 2px solid rgba(102, 126, 234, 0.6);
}

.partOfSpeech {
  color: rgba(102, 126, 234, 0.9) !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  letter-spacing: 0.8px;
}

.meaning {
  color: rgba(255, 255, 255, 0.85) !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  font-weight: 400 !important;
}

/* 滚动条样式 */
.cardBody::WebkitScrollbar {
  width: 6px;
}

.cardBody::WebkitScrollbarTrack {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.cardBody::WebkitScrollbarThumb {
  background: rgba(102, 126, 234, 0.6);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.cardBody::WebkitScrollbarThumb:hover {
  background: rgba(102, 126, 234, 0.8);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cardContent {
    max-width: 260px;
    min-width: 220px;
  }

  .cardContent :global(.ant-card-head) {
    padding: 10px 12px !important;
  }

  .cardContent :global(.ant-card-body) {
    padding: 12px !important;
  }

  .wordTitle {
    font-size: 16px !important;
  }

  .phonetic {
    font-size: 12px !important;
  }

  .cardBody {
    max-height: 140px;
  }

  .sectionTitle {
    font-size: 12px !important;
  }

  .translationText {
    font-size: 13px !important;
  }

  .meaning {
    font-size: 12px !important;
  }

  .definitionItem {
    padding: 4px 8px;
  }
}
