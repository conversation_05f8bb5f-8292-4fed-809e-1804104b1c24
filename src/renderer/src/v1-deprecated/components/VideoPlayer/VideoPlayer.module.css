/* VideoSection 组件样式 - 现代化沉浸式设计 */

.videoSection {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
  width: 100%;
  height: 100%;
  background: var(--darker-bg);
}

.videoContainer {
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  /* 完全移除卡片样式，实现无缝沉浸式体验 */
  border-radius: 0;
  overflow: hidden;
  background: #000;
  /* 移除所有阴影效果 */
  box-shadow: none;
  /* 移除边框和背景模糊 */
  border: none;
  backdrop-filter: none;
  /* 🔧 移除固定宽高比，避免溢出问题 - Remove fixed aspect ratio to avoid overflow issues */
  /* aspect-ratio: 16/9; */
  /* 保持平滑过渡 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.videoPlayer {
  width: 100%;
  height: 100%;
  display: block;
  background: #000;
  /* 移除圆角，实现无缝体验 */
  border-radius: 0;
  object-fit: contain;
}

.videoPlaceholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border: 2px dashed rgba(255, 255, 255, 0.1);
  /* 移除圆角，保持无缝设计 */
  border-radius: 0;
  transition: var(--transition);
  cursor: pointer;
}

.videoPlaceholder:hover {
  border-color: var(--accent-color);
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
  transform: translateY(-2px);
}

/* 字幕覆盖层样式 - 支持自由定位的字幕容器 */
.subtitleOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10; /* 在视频上方，但在控制器下方 */
  width: 100%;
  height: 100%;
  pointer-events: none; /* 容器本身不接收点击事件，但允许子元素接收 */
  /* 移除flex布局，让字幕组件自由定位 */
}

/* 控制组件覆盖层样式 - 覆盖在视频上，但允许字幕点击 */
.controlsOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  z-index: 20; /* 确保控制器在字幕上方 */
  width: 100%;
  height: 100%;
  pointer-events: none; /* 默认不接收点击事件，让字幕可以被点击 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .videoSection {
    flex: 2; /* 移动端视频区域占2份 */
    min-height: 150px;
  }
}
