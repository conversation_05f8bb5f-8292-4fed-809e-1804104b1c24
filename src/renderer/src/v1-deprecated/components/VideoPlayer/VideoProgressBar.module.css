/* VideoProgressBar 组件样式 - 简洁进度条设计 */

.progressContainer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 15;
  pointer-events: auto;
}

.progressContainer.enhanced {
  height: 8px;
  padding: 2px 0;
}

.progressTrack {
  position: relative;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0;
  overflow: hidden;
}

.progressContainer.enhanced .progressTrack {
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.3);
}

.progressFill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #ffffff;
  border-radius: inherit;
  transition: all 0.2s ease;
}

.progressHandle {
  position: absolute;
  top: 50%;
  width: 14px;
  height: 14px;
  background: #ffffff;
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.progressContainer.enhanced .progressHandle {
  transform: translate(-50%, -50%) scale(1);
}

.timeTooltip {
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.4);
  animation: fadeInUp 0.2s ease-out;
}

.timeText {
  color: white !important;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  font-weight: 500;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .progressContainer {
    height: 6px;
  }

  .progressContainer.enhanced {
    height: 10px;
  }

  .progressTrack {
    height: 6px;
  }

  .progressContainer.enhanced .progressTrack {
    height: 8px;
  }

  .progressHandle {
    width: 16px;
    height: 16px;
  }

  .timeTooltip {
    padding: 4px 8px;
    font-size: 11px;
  }

  .timeText {
    font-size: 11px;
  }
}
