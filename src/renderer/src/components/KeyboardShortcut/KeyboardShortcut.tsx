import { useTheme } from '@renderer/contexts'
import React from 'react'

interface KeyboardShortcutProps {
  shortcut: string
  className?: string
  style?: React.CSSProperties
}

/**
 * 显示键盘快捷键的组件，使用 v2 主题系统
 * 基于 v1 的 KeyboardShortcut 组件，适配 v2 架构
 */
export function KeyboardShortcut({
  shortcut,
  className,
  style
}: KeyboardShortcutProps): React.JSX.Element {
  const { token } = useTheme()
  const isMacOS = window.navigator.platform.toLowerCase().includes('mac')

  // 格式化快捷键显示 - 与 v1 保持一致
  const formatShortcut = (key: string): string => {
    if (isMacOS) {
      return key
        .replace(/Ctrl\+/g, '⌘')
        .replace(/Alt\+/g, '⌥')
        .replace(/Shift\+/g, '⇧')
        .replace(/Enter/g, '↩')
        .replace(/Escape/g, 'Esc')
        .replace(/ArrowUp/g, '↑')
        .replace(/ArrowDown/g, '↓')
        .replace(/ArrowLeft/g, '←')
        .replace(/ArrowRight/g, '→')
    } else {
      // Windows/Linux 样式
      return key
        .replace(/Enter/g, '↵')
        .replace(/Escape/g, 'Esc')
        .replace(/ArrowUp/g, '↑')
        .replace(/ArrowDown/g, '↓')
        .replace(/ArrowLeft/g, '←')
        .replace(/ArrowRight/g, '→')
    }
  }

  // 将快捷键拆分为单个按键
  const keys = formatShortcut(shortcut).split('+')

  return (
    <span className={className} style={style}>
      {keys.map((key, index) => (
        <React.Fragment key={index}>
          <kbd
            style={{
              display: 'inline-block',
              padding: `${token.paddingXXS}px ${token.paddingXS}px`,
              margin: `0 ${token.marginXXS}px`,
              backgroundColor: token.colorBgContainer,
              border: `1px solid ${token.colorBorderSecondary}`,
              borderRadius: token.borderRadiusSM,
              boxShadow: `0 1px 0 ${token.colorBorderSecondary}`,
              fontSize: token.fontSizeSM,
              fontFamily: 'Monaco, "SF Mono", Consolas, monospace',
              lineHeight: 1,
              whiteSpace: 'nowrap'
            }}
          >
            {key}
          </kbd>
          {index < keys.length - 1 && <span style={{ margin: `0 ${token.marginXXS}px` }}>+</span>}
        </React.Fragment>
      ))}
    </span>
  )
}
