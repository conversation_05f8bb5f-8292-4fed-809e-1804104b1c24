import {
  ApiOutlined,
  BugOutlined,
  CloudUploadOutlined,
  DeleteOutlined,
  FolderOpenOutlined,
  ThunderboltOutlined,
  ToolOutlined
} from '@ant-design/icons'
import { useTheme } from '@renderer/contexts'
import { Button, Card, Divider, message, Space, Switch, Typography } from 'antd'
import React from 'react'

const { Text, Title } = Typography

export function ToolSettings(): React.JSX.Element {
  const { token, styles } = useTheme()

  // TODO: 从状态管理或用户偏好中获取当前设置
  const [autoBackup, setAutoBackup] = React.useState(true)
  const [enableDebugMode, setEnableDebugMode] = React.useState(false)
  const [hardwareAcceleration, setHardwareAcceleration] = React.useState(true)
  const [enableLogging, setEnableLogging] = React.useState(false)

  const handleTestConnection = (): void => {
    // TODO: 实现连接测试逻辑
    const hide = message.loading('正在测试连接...', 0)

    // 模拟异步操作
    setTimeout(() => {
      hide()
      // 这里可以根据实际测试结果显示成功或失败
      message.success('连接测试成功')
    }, 1000)
  }

  const handleClearLogs = (): void => {
    // TODO: 实现清除日志逻辑
    message.success('日志已清除')
  }

  const handleOpenLogFolder = (): void => {
    // TODO: 实现打开日志文件夹逻辑
    message.info('正在打开日志文件夹...')
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <ToolOutlined style={{ color: token.colorPrimary }} />
          <span>工具设置</span>
        </div>
      }
      style={styles.settingsSectionCard}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 性能设置 */}
        <div>
          <Title level={5} style={{ margin: 0, marginBottom: 16 }}>
            <ThunderboltOutlined style={{ marginRight: 8, color: token.colorPrimary }} />
            性能设置
          </Title>

          <div
            style={{
              padding: '16px',
              backgroundColor: token.colorBgContainer,
              borderRadius: token.borderRadius,
              border: `1px solid ${token.colorBorder}`
            }}
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'flex-start',
                justifyContent: 'space-between',
                padding: '8px 0'
              }}
            >
              <div style={{ flex: 1, marginRight: 24 }}>
                <Text strong style={{ display: 'block', marginBottom: 4 }}>
                  启用硬件加速
                </Text>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  利用GPU加速视频播放，可能提高性能但增加功耗
                </Text>
              </div>
              <Switch
                checked={hardwareAcceleration}
                onChange={setHardwareAcceleration}
                checkedChildren="启用"
                unCheckedChildren="禁用"
              />
            </div>
          </div>
        </div>

        <Divider />

        {/* 备份设置 */}
        <div>
          <Title level={5} style={{ margin: 0, marginBottom: 16 }}>
            <CloudUploadOutlined style={{ marginRight: 8, color: token.colorPrimary }} />
            备份设置
          </Title>

          <div
            style={{
              padding: '16px',
              backgroundColor: token.colorBgContainer,
              borderRadius: token.borderRadius,
              border: `1px solid ${token.colorBorder}`
            }}
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'flex-start',
                justifyContent: 'space-between',
                padding: '8px 0'
              }}
            >
              <div style={{ flex: 1, marginRight: 24 }}>
                <Text strong style={{ display: 'block', marginBottom: 4 }}>
                  自动备份设置
                </Text>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  每天自动备份您的设置和偏好配置
                </Text>
              </div>
              <Switch
                checked={autoBackup}
                onChange={setAutoBackup}
                checkedChildren="启用"
                unCheckedChildren="禁用"
              />
            </div>
          </div>
        </div>

        <Divider />

        {/* 开发者工具 */}
        <div>
          <Title level={5} style={{ margin: 0, marginBottom: 16 }}>
            <BugOutlined style={{ marginRight: 8, color: token.colorPrimary }} />
            开发者工具
          </Title>

          <div
            style={{
              padding: '16px',
              backgroundColor: token.colorBgContainer,
              borderRadius: token.borderRadius,
              border: `1px solid ${token.colorBorder}`
            }}
          >
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  justifyContent: 'space-between',
                  padding: '8px 0'
                }}
              >
                <div style={{ flex: 1, marginRight: 24 }}>
                  <Text strong style={{ display: 'block', marginBottom: 4 }}>
                    启用调试模式
                  </Text>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    显示详细的调试信息，仅供开发使用
                  </Text>
                </div>
                <Switch
                  checked={enableDebugMode}
                  onChange={setEnableDebugMode}
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                />
              </div>

              <div
                style={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  justifyContent: 'space-between',
                  padding: '8px 0'
                }}
              >
                <div style={{ flex: 1, marginRight: 24 }}>
                  <Text strong style={{ display: 'block', marginBottom: 4 }}>
                    启用日志记录
                  </Text>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    记录应用运行日志，用于问题诊断
                  </Text>
                </div>
                <Switch
                  checked={enableLogging}
                  onChange={setEnableLogging}
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                />
              </div>

              {enableLogging && (
                <div
                  style={{
                    paddingTop: '16px',
                    borderTop: `1px solid ${token.colorBorder}`
                  }}
                >
                  <Space>
                    <Button
                      onClick={handleOpenLogFolder}
                      type="primary"
                      icon={<FolderOpenOutlined />}
                    >
                      打开日志文件夹
                    </Button>
                    <Button onClick={handleClearLogs} icon={<DeleteOutlined />}>
                      清除日志
                    </Button>
                  </Space>
                </div>
              )}
            </Space>
          </div>
        </div>

        <Divider />

        {/* 连接测试 */}
        <div>
          <Title level={5} style={{ margin: 0, marginBottom: 16 }}>
            <ApiOutlined style={{ marginRight: 8, color: token.colorPrimary }} />
            连接测试
          </Title>

          <div
            style={{
              padding: '16px',
              backgroundColor: token.colorBgContainer,
              borderRadius: token.borderRadius,
              border: `1px solid ${token.colorBorder}`
            }}
          >
            <Text style={{ display: 'block', marginBottom: '16px', color: token.colorText }}>
              测试应用与服务器的连接状态
            </Text>
            <Button onClick={handleTestConnection} type="primary" icon={<ApiOutlined />}>
              测试连接
            </Button>
          </div>
        </div>
      </Space>
    </Card>
  )
}

export default ToolSettings
