import EnUs from '../../renderer/src/i18n/locales/en-us.json'
import Ja<PERSON> from '../../renderer/src/i18n/locales/ja-jp.json'
import RuRu from '../../renderer/src/i18n/locales/ru-ru.json'
import ZhCn from '../../renderer/src/i18n/locales/zh-cn.json'
import ZhTw from '../../renderer/src/i18n/locales/zh-tw.json'

const locales = {
  'en-US': EnUs,
  'zh-CN': ZhCn,
  'zh-TW': ZhTw,
  'ja-JP': JaJP,
  'ru-RU': RuRu
}

export { locales }
