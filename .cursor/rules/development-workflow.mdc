---
description: 
globs: 
alwaysApply: false
---
# 开发工作流程指南

EchoLab 项目的开发工作流程和最佳实践。

## 开发环境设置

### 1. 依赖安装
```bash
pnpm install
```

### 2. 开发模式启动
```bash
pnpm dev
```

### 3. 构建应用
```bash
pnpm build:mac    # macOS
pnpm build:win    # Windows  
pnpm build:linux  # Linux
```

## 项目配置文件

### 核心配置
- **[package.json](mdc:package.json)** - 项目依赖和脚本
- **[electron.vite.config.ts](mdc:electron.vite.config.ts)** - Vite 和 Electron 构建配置
- **[tsconfig.json](mdc:tsconfig.json)** - TypeScript 主配置
- **[tsconfig.web.json](mdc:tsconfig.web.json)** - 渲染进程 TypeScript 配置
- **[tsconfig.node.json](mdc:tsconfig.node.json)** - 主进程 TypeScript 配置

### 代码质量工具
- **[eslint.config.mjs](mdc:eslint.config.mjs)** - ESLint 配置
- **[.prettierrc.yaml](mdc:.prettierrc.yaml)** - Prettier 格式化配置
- **[.editorconfig](mdc:.editorconfig)** - 编辑器配置

## 开发脚本

### 代码质量检查
```bash
pnpm lint        # ESLint 检查
pnpm format      # Prettier 格式化
pnpm typecheck   # TypeScript 类型检查
```

### 类型检查分离
```bash
pnpm typecheck:node  # 主进程类型检查
pnpm typecheck:web   # 渲染进程类型检查
```

## 文件结构约定

### 1. 组件命名
- 使用 PascalCase：`VideoPlayer.tsx`
- 组件文件名与组件名一致

### 2. Hook 命名
- 使用 camelCase，以 `use` 开头：`useVideoPlayer.ts`

### 3. 工具函数
- 使用 camelCase：`subtitleParser.ts`
- 按功能分组到不同文件

### 4. 类型定义
- 主要类型在 [types/index.ts](mdc:src/renderer/src/types/index.ts)
- 共享类型在 [types/shared.ts](mdc:src/renderer/src/types/shared.ts)

## 代码规范

### 1. TypeScript 使用
- 严格类型检查
- 避免使用 `any` 类型
- 为所有 Props 定义接口

### 2. React 最佳实践
- 使用函数组件和 Hooks
- 合理使用 `useMemo` 和 `useCallback` 优化性能
- 组件职责单一

### 3. 性能优化
- 使用 `React.memo` 避免不必要的重渲染
- 大数据列表使用虚拟滚动
- 防抖处理频繁操作

## 调试和测试

### 1. 开发工具
- Electron DevTools 自动打开（开发模式）
- React DevTools 支持
- 详细的控制台日志

### 2. 错误处理
- 完善的 try-catch 块
- 用户友好的错误提示
- 详细的错误日志记录

## 构建和发布

### 1. 构建配置
- **[electron-builder.yml](mdc:electron-builder.yml)** - 应用打包配置
- **[dev-app-update.yml](mdc:dev-app-update.yml)** - 开发环境更新配置

### 2. 平台特定构建
```bash
pnpm build:mac    # 生成 .dmg 文件
pnpm build:win    # 生成 .exe 安装包
pnpm build:linux  # 生成 .AppImage 文件
```

## 常见问题解决

### 1. 播放进度丢失
参考 [PROGRESS_FIX_SUMMARY.md](mdc:PROGRESS_FIX_SUMMARY.md) 中的修复方案。

### 2. 文件访问问题
参考 [FILE_RECOVERY_IMPLEMENTATION.md](mdc:FILE_RECOVERY_IMPLEMENTATION.md) 中的文件恢复机制。

### 3. 性能优化
- 检查组件重渲染次数
- 优化大数据处理
- 使用 React DevTools Profiler

## 版本控制

### Git 工作流
- 功能分支开发
- 提交信息使用中文
- 代码审查后合并

### 忽略文件
参考 [.gitignore](mdc:.gitignore) 配置。

## IDE 推荐设置

### VSCode 扩展
- ESLint
- Prettier
- TypeScript Importer
- Electron Debug

### 配置建议
- 启用自动格式化
- 配置 TypeScript 严格模式
- 使用 Prettier 作为默认格式化工具
