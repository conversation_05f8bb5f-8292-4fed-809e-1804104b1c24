---
description: 
globs: 
alwaysApply: false
---
# EchoLab 开发指南

## 📋 项目概述
EchoLab 是一个基于 Electron + React + TypeScript 的视频学习工具，支持字幕显示、单词查询等功能。

## 🖥️ 系统要求与分辨率支持

### 最小分辨率要求
**重要：EchoLab 应用最小支持分辨率为 768px 宽度。**

#### 支持的分辨率范围
- **最小分辨率**：768px × 576px
- **推荐分辨率**：1024px × 768px 及以上
- **最佳体验**：1920px × 1080px 及以上

#### 响应式设计断点
```css
/* 应用最小分辨率 (768px-900px) */
@media (max-width: 900px) and (min-width: 768px) { }

/* 中等屏幕 (901px-1024px) */
@media (max-width: 1024px) and (min-width: 901px) { }

/* 大屏幕 (1025px以上) */
@media (min-width: 1025px) { }
```

#### Grid系统配置
基于Ant Design Grid系统，针对768px+分辨率优化：

```typescript
// ✅ 正确的Grid配置（移除xs断点）
<Row gutter={[{ sm: 20, md: 24, lg: 24 }, { sm: 20, md: 24, lg: 24 }]}>
  <Col sm={12} md={8} lg={6} xl={4}>
    {/* 内容 */}
  </Col>
</Row>

// ❌ 错误的配置（包含小于768px的断点）
<Row gutter={[{ xs: 16, sm: 20, md: 24 }, { xs: 16, sm: 20, md: 24 }]}>
  <Col xs={24} sm={12} md={8} lg={6} xl={4}>
    {/* 内容 */}
  </Col>
</Row>
```

#### 开发注意事项
1. **媒体查询限制**：所有CSS媒体查询都应使用 `min-width: 768px`
2. **Grid断点**：不使用 `xs` 断点，从 `sm` (768px) 开始
3. **测试要求**：确保在768px宽度下应用功能完整且布局正常
4. **用户提示**：对于低于768px的设备，考虑显示分辨率不支持提示

#### 设计原则
- **渐进式增强**：从768px基准向更大屏幕逐步优化
- **内容优先**：确保在最小支持分辨率下所有核心功能可用
- **性能考虑**：避免为不支持的小屏幕添加额外的CSS和JS代码

## 🎯 性能优化最佳实践

### 1. 组件渲染优化策略

#### 问题识别
- **症状**: 组件在视频播放时频繁渲染（每300ms一次）
- **原因**: 直接在父组件中使用响应时间变化的 hooks（如 `useVideoTime()`）
- **影响**: 导致整个组件树不必要的重新渲染，影响性能

#### 解决方案：时间敏感组件分离模式
```typescript
// ❌ 错误做法：在主组件中直接使用时间hook
function VideoPlayer() {
  const currentTime = useVideoTime() // 导致频繁渲染
  const currentSubtitle = getCurrentSubtitle(currentTime)
  
  return (
    <div>
      <ReactPlayer />
      <SubtitleDisplay subtitle={currentSubtitle} />
    </div>
  )
}

// ✅ 正确做法：将时间敏感逻辑分离到独立组件
function VideoPlayer() {
  // 不直接使用时间hook，避免频繁渲染
  return (
    <div>
      <ReactPlayer />
      <SubtitleOverlay /> {/* 独立处理时间变化 */}
    </div>
  )
}

function SubtitleOverlay() {
  const currentTime = useVideoTime() // 在这里处理时间变化
  const currentSubtitle = getCurrentSubtitle(currentTime)
  
  return <SubtitleDisplay subtitle={currentSubtitle} />
}
```

#### 核心原则
1. **单一职责**: 每个组件只负责一种类型的状态变化
2. **响应式分离**: 将频繁变化的状态从稳定的UI组件中分离
3. **渲染边界**: 在需要响应变化的最小范围内使用响应式hooks

### 2. Context 和 Hook 架构设计

#### 状态管理层次
```typescript
// 1. Context层 - 集中状态管理，基于Ref避免渲染
const VideoPlayerContext = {
  currentTimeRef: useRef(0),
  isPlayingRef: useRef(false),
  // 订阅机制用于精确控制渲染
  subscribeToTime: (callback) => void,
  subscribeToPlayState: (callback) => void
}

// 2. Hook层 - 根据需求选择不同的访问方式
// 需要响应变化的组件使用：
const currentTime = useVideoTime() // 会重新渲染

// 不需要响应变化的组件使用：
const { currentTimeRef } = useVideoStateRefs() // 不会重新渲染
```

#### Hook 设计模式
1. **响应式Hook**: 适用于需要实时更新UI的组件
2. **Ref Hook**: 适用于只需要读取最新值但不需要重新渲染的场景
3. **控制Hook**: 提供状态修改方法，通常是稳定的函数引用

### 3. React.memo 优化策略

#### 比较函数设计原则
```typescript
// ✅ 精简的比较函数
const arePropsEqual = (prev: Props, next: Props): boolean => {
  // 只比较真正影响渲染的属性
  if (prev.displayMode !== next.displayMode) return false
  
  // 跳过回调函数比较（通常是稳定的）
  // 跳过频繁变化但不影响UI的属性
  return true
}

// ❌ 过度复杂的比较函数
const arePropsEqual = (prev: Props, next: Props): boolean => {
  // 比较所有属性，包括频繁变化的时间值
  if (prev.currentTime !== next.currentTime) return false // 导致频繁渲染
  // ... 过多的比较逻辑
}
```

### 4. 组件架构模式

#### 推荐的组件分层
```
VideoSection (容器组件)
├── VideoPlayer (稳定的播放器外壳)
│   ├── ReactPlayer (第三方播放器)
│   ├── SubtitleOverlay (时间敏感的字幕层)
│   └── VideoControls (控制界面)
└── VideoControlsCompact (外部控制栏)
```

#### 关键设计决策
1. **VideoPlayer**: 不直接处理时间变化，保持渲染稳定
2. **SubtitleOverlay**: 专门处理字幕相关的时间响应
3. **控制组件**: 使用Context直接获取状态，减少props传递

### 5. 性能监控和调试

#### 组件渲染日志
```typescript
// 在关键组件中添加渲染日志
RendererLogger.componentRender({
  component: 'ComponentName',
  props: {
    // 只记录关键属性，避免记录频繁变化的值
    isPlaying,
    isVideoLoaded,
    // 不记录 currentTime 等频繁变化的属性
  }
})
```

#### 性能分析指标
- **目标**: 播放时VideoPlayer组件不应频繁渲染
- **监控**: 通过控制台日志观察组件渲染频率
- **优化**: 确保时间敏感的渲染只发生在必要的组件中

### 6. 开发建议

#### 新功能开发流程
1. **分析状态依赖**: 确定组件需要响应哪些状态变化
2. **选择合适的Hook**: 根据是否需要重新渲染选择hook类型
3. **设计组件边界**: 将频繁变化的逻辑隔离到独立组件
4. **添加性能监控**: 在关键组件中添加渲染日志
5. **测试渲染性能**: 确保组件不会过度渲染

#### 常见陷阱避免
- ❌ 在容器组件中直接使用 `useVideoTime()`
- ❌ 在memo比较函数中比较所有props
- ❌ 传递频繁变化的props给稳定组件
- ❌ 忽略组件渲染日志的警告信息

## 📚 相关资源

### 核心文件位置
- Context实现: `src/renderer/src/contexts/VideoPlayerContext.tsx`
- Hook实现: `src/renderer/src/hooks/useVideoPlayerHooks.ts`
- 主要组件: `src/renderer/src/components/VideoPlayer/`

### 性能优化相关
- 组件渲染监控: `src/renderer/src/utils/logger.ts`
- Context订阅机制: 用于精确控制重新渲染的范围

---

*最后更新: 2025-06-01*
*优化成果: VideoPlayer组件播放时渲染频率从300ms一次优化到仅在必要状态变化时渲染*

## React 性能优化指南

### 1. 组件重新渲染优化

#### 问题识别：频繁重新渲染
在开发过程中，发现了以下导致组件频繁重新渲染的常见问题：

**SubtitleV3 组件拖拽时频繁渲染问题**：
- **原因**：拖拽过程中每次鼠标移动都直接调用 `updateSubtitleState`
- **症状**：拖拽时控制台大量渲染日志，影响性能和用户体验
- **解决方案**：实施节流机制和优化依赖项

**VideoPlayer 组件鼠标移动时频繁渲染问题**：
- **原因**：`onMouseMove` 事件处理器每次都调用 `setShowControls(true)`
- **症状**：鼠标在播放器区域移动时触发大量重新渲染
- **解决方案**：添加状态检查和节流机制

#### 优化策略

##### 1. 节流机制（Throttling）
```typescript
// 节流更新函数示例
const throttledUpdate = useCallback((newState: SubtitleMarginsState) => {
  const now = Date.now()
  const timeSinceLastUpdate = now - lastUpdateTimeRef.current
  
  // 限制更新频率为60fps（16ms间隔）
  if (timeSinceLastUpdate < 16) {
    pendingUpdateRef.current = newState
    
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current)
    }
    
    updateTimeoutRef.current = setTimeout(() => {
      if (pendingUpdateRef.current) {
        updateSubtitleState(pendingUpdateRef.current)
        lastUpdateTimeRef.current = Date.now()
        pendingUpdateRef.current = null
      }
    }, 16 - timeSinceLastUpdate)
  } else {
    // 立即更新
    updateSubtitleState(newState)
    lastUpdateTimeRef.current = now
  }
}, [updateSubtitleState])
```

##### 2. 状态检查优化
```typescript
// 鼠标移动处理优化
const handleMouseMove = useCallback((): void => {
  const now = Date.now()
  const timeSinceLastMove = now - lastMouseMoveTimeRef.current
  
  // 如果已经显示控制栏且时间间隔小于阈值，则跳过更新
  if (showControls && timeSinceLastMove < 100) {
    return
  }
  
  // 只有在控制栏未显示时才更新状态
  if (!showControls) {
    setShowControls(true)
  }
}, [showControls, isUserInteracting])
```

##### 3. useMemo 依赖项优化
```typescript
// 避免动态依赖项导致的频繁重计算
const parentDimensions = useMemo(() => {
  const parent = containerRef.current?.parentElement
  const dimensions = {
    width: parent?.clientWidth || 0,
    height: parent?.clientHeight || 0
  }
  parentDimensionsRef.current = dimensions
  return dimensions
}, []) // 移除动态依赖项，使用 ref 存储最新值
```

##### 4. useEffect 依赖项优化
```typescript
// 使用更稳定的依赖项
useEffect(() => {
  const isDragging = dragAndResizeProps.isDragging
  const isResizing = dragAndResizeProps.isResizing
  
  if (isDragging || isResizing) {
    // 事件处理逻辑
  }
}, [
  dragAndResizeProps.isDragging, 
  dragAndResizeProps.isResizing,
  dragAndResizeProps.handleMouseMove,
  dragAndResizeProps.handleMouseUp
]) // 只依赖具体需要的属性，而不是整个对象
```

##### 5. memo 组件优化
```typescript
// 自定义比较函数，只比较真正影响渲染的属性
const arePropsEqual = (prevProps: Props, nextProps: Props): boolean => {
  // 只比较关键属性，忽略回调函数等稳定属性
  if (prevProps.currentSubtitle?.startTime !== nextProps.currentSubtitle?.startTime) return false
  if (prevProps.currentSubtitle?.text !== nextProps.currentSubtitle?.text) return false
  if (prevProps.isPlaying !== nextProps.isPlaying) return false
  if (prevProps.displayMode !== nextProps.displayMode) return false
  
  return true
}

const MemoizedComponent = memo(Component, arePropsEqual)
```

#### 开发检查清单

在开发新功能或修改现有组件时，请检查以下项目：

1. **事件处理器优化**：
   - [ ] 高频事件（如 `onMouseMove`、`onScroll`）是否添加了节流机制
   - [ ] 状态更新前是否检查了当前状态，避免无效更新
   - [ ] 是否合理使用了 `useCallback` 来稳定回调函数

2. **状态管理优化**：
   - [ ] 避免在拖拽/动画过程中频繁调用状态更新函数
   - [ ] 使用 `useRef` 存储不需要触发重新渲染的值
   - [ ] 在操作结束时批量更新状态

3. **依赖项优化**：
   - [ ] `useMemo` 和 `useCallback` 的依赖项是否最小化
   - [ ] 避免将整个对象作为依赖项，只依赖具体属性
   - [ ] 使用 `useRef` 避免动态计算的依赖项

4. **组件 memo 优化**：
   - [ ] 对于复杂组件使用 `memo` 包装
   - [ ] 提供自定义比较函数，只比较影响渲染的属性
   - [ ] 避免在 props 中传递匿名函数或对象

#### 性能监控

使用以下方法监控组件性能：

1. **开发环境日志**：
   ```typescript
   // 在组件开头添加渲染日志
   RendererLogger.componentRender({
     component: 'ComponentName',
     props: { /* 关键 props */ }
   })
   ```

2. **React DevTools Profiler**：
   - 使用 Profiler 标识性能瓶颈
   - 重点关注频繁重新渲染的组件

3. **性能指标**：
   - 拖拽操作应保持 60fps 流畅度
   - 鼠标交互响应时间应小于 100ms
   - 避免在 1 秒内重新渲染超过 10 次

#### 最佳实践总结

1. **谨慎使用高频事件**：始终为高频事件添加节流或防抖机制
2. **状态更新前检查**：避免设置相同的状态值
3. **使用 ref 存储临时数据**：不需要触发重新渲染的数据使用 `useRef`
4. **优化依赖项**：保持依赖数组最小化和稳定
5. **合理使用 memo**：对复杂组件使用 memo 并提供优化的比较函数

这些优化策略帮助我们在保持功能完整性的同时，显著提升了应用的性能和用户体验。

## 类型定义管理规则

### 核心原则：统一类型定义
**重要：所有 Electron API 的类型定义必须统一在 `src/preload/index.d.ts` 中管理。**

### 类型定义层次结构

1. **主要类型定义文件**
   - `src/preload/index.d.ts` - **唯一的全局 API 类型定义文件**
   - `src/types/shared.ts` - 主进程和渲染进程共享的业务类型

2. **禁止的做法**
   - ❌ 不要在 `src/renderer/src/env.d.ts` 中重复定义 API 类型
   - ❌ 不要在 `src/renderer/src/types/global.d.ts` 中重复定义 API 类型  
   - ❌ 不要在业务文件中使用 `declare global` 重新声明 API 类型
   - ❌ 不要创建多个全局类型定义文件

### API 类型定义规范

当需要添加新的 Electron API 时，按以下步骤操作：

1. **在 `src/preload/index.ts` 中实现 API**
   ```typescript
   const newAPI = {
     someMethod: (param: string): Promise<Result> => 
       ipcRenderer.invoke('new:some-method', param)
   }
   ```

2. **在 `src/preload/index.d.ts` 中添加类型定义**
   ```typescript
   interface NewAPI {
     someMethod: (param: string) => Promise<Result>
   }
   
   declare global {
     interface Window {
       api: {
         // ... 现有 API
         newAPI: NewAPI
       }
     }
   }
   ```

3. **在 `src/types/shared.ts` 中定义业务类型**
   ```typescript
   export interface Result {
     success: boolean
     data?: unknown
     error?: string
   }
   ```

### 类型导入规范

在渲染进程中使用类型时：

```typescript
// ✅ 正确：从 shared 导入业务类型
import type { RecentPlayItem, StoreSettings } from '@renderer/types'

// ✅ 正确：全局 API 类型自动可用，无需导入
const result = await window.api.store.getRecentPlays()

// ❌ 错误：不要导入或重新声明全局类型
import '@renderer/types/global' // 已删除
```

### 文件结构约定

```
src/
├── preload/
│   ├── index.ts          # API 实现
│   └── index.d.ts        # 🔑 唯一的全局类型定义
├── types/
│   └── shared.ts         # 业务类型定义
└── renderer/src/
    ├── types/
    │   └── index.ts      # 渲染进程特有类型
    └── hooks/
        └── useRecentPlays.ts  # 业务逻辑，直接使用全局类型
```

## 组件开发规范

### 1. 组件命名和结构
- 使用 PascalCase：`VideoPlayer.tsx`
- 组件文件名与组件名一致
- 每个组件一个文件，复杂组件可建立文件夹

### 2. Hook 开发规范
- 使用 camelCase，以 `use` 开头：`useVideoPlayer.ts`
- 返回对象包含状态和操作方法
- 使用 TypeScript 严格类型定义

### 3. 错误处理规范
- 所有异步操作必须包含 try-catch
- 提供用户友好的错误信息
- 记录详细的控制台日志

## 性能优化指南

### 1. React 性能优化
- 合理使用 `useMemo` 和 `useCallback`
- 使用 `React.memo` 避免不必要的重渲染
- 大数据列表使用虚拟滚动

### 2. 类型检查优化
- 避免使用 `any` 类型
- 为所有 Props 定义接口
- 使用严格的 TypeScript 配置

## 代码质量保证

### 1. 开发工具链
```bash
pnpm lint        # ESLint 检查
pnpm format      # Prettier 格式化  
pnpm typecheck   # TypeScript 类型检查
```

### 2. 提交前检查
- 运行类型检查：`pnpm typecheck`
- 运行代码格式化：`pnpm format`
- 确保没有 ESLint 错误

## 调试和测试

### 1. 开发模式调试
- Electron DevTools 自动打开
- React DevTools 支持
- 详细的控制台日志

### 2. 类型安全验证
- 定期运行 `pnpm typecheck:web` 和 `pnpm typecheck:node`
- 确保主进程和渲染进程类型一致性

## React Hook 性能优化最佳实践

### 1. useEffect 依赖优化

#### 问题描述
React 不建议将函数作为 useEffect 的依赖，因为函数在每次渲染时都会重新创建，导致 effect 不必要地重新执行。

#### 解决方案

**方案一：使用 useRef 存储函数引用**
```typescript
// ❌ 不好的做法 - 函数作为依赖
useEffect(() => {
  initialize();
}, [restoreVideoState, restoreSubtitles, getRecentPlayByPath]);

// ✅ 好的做法 - 使用 ref 存储函数引用
const restoreVideoStateRef = useRef(restoreVideoState);
const restoreSubtitlesRef = useRef(restoreSubtitles);

// 更新 ref 的值
restoreVideoStateRef.current = restoreVideoState;
restoreSubtitlesRef.current = restoreSubtitles;

useEffect(() => {
  const initialize = async () => {
    // 使用 ref.current 调用函数
    restoreVideoStateRef.current(currentTime, playbackRate, volume);
  };
  initialize();
}, [originalFilePath, videoFile]); // 只依赖基本数据类型
```

**方案二：将函数移到 useEffect 内部**
```typescript
// ✅ 最佳做法 - 函数定义在 effect 内部
useEffect(() => {
  const detectAndLoadSubtitles = async (videoPath: string) => {
    // 函数逻辑
  };
  
  const initialize = async () => {
    await detectAndLoadSubtitles(originalFilePath);
  };
  
  initialize();
}, [originalFilePath, videoFile]); // 无函数依赖
```

### 2. useCallback 使用指南

#### 何时使用 useCallback

1. **防止子组件不必要重渲染**
```typescript
// 子组件使用 React.memo 时
const Child = React.memo(({ onClick }) => {
  return <button onClick={onClick}>Click</button>;
});

function Parent() {
  const handleClick = useCallback(() => {
    console.log('clicked');
  }, []); // 稳定的函数引用
  
  return <Child onClick={handleClick} />;
}
```

2. **作为其他 Hook 的依赖**
```typescript
const fetchData = useCallback(() => {
  return api.getData(userId);
}, [userId]);

useEffect(() => {
  fetchData();
}, [fetchData]); // 安全的函数依赖
```

3. **传递给自定义 Hook**
```typescript
const handleSearch = useCallback(() => {
  performSearch(query);
}, [query]);

useDebounce(handleSearch, 500);
```

#### 何时不需要 useCallback

1. **函数不作为 props 传递**
2. **子组件未使用 React.memo**
3. **函数仅在组件内部使用**
4. **函数创建成本很低**

#### 性能考虑

- useCallback 本身有内存和计算成本
- 只在确实需要时使用
- 优先考虑代码结构重构而非过度优化

### 3. 代码重构原则

#### 优先级顺序
1. **重构代码结构** - 将函数移到合适位置
2. **使用 useRef** - 存储函数引用避免依赖
3. **使用 useCallback** - 最后的优化手段

#### 实际案例
在 `usePlayStateInitializer` Hook 中：
- 原始问题：多个函数作为 useEffect 依赖
- 解决方案：结合使用 useRef 和函数内部定义
- 结果：依赖数组只包含基本数据类型和稳定引用

### 4. 依赖数组最佳实践

#### 理想的依赖数组
```typescript
useEffect(() => {
  // effect 逻辑
}, [
  // 基本数据类型
  originalFilePath,    // string
  videoFile,          // string
  subtitles.length,   // number
  isEnabled,          // boolean
  
  // 稳定引用
  saveStateRef        // ref 对象
]);
```

#### 避免的依赖类型
- 函数引用（除非使用 useCallback 优化）
- 对象引用（除非使用 useMemo 优化）
- 数组引用（除非使用 useMemo 优化）

### 5. 性能优化检查清单

- [ ] useEffect 依赖数组中无函数引用
- [ ] 使用 useRef 存储外部函数引用
- [ ] 考虑将函数移到 effect 内部
- [ ] 仅在必要时使用 useCallback
- [ ] 子组件使用 React.memo 时才优化 props 函数
- [ ] 依赖数组尽量包含基本数据类型
- [ ] 避免过度优化，保持代码可读性

### 6. 调试技巧

#### 检查 effect 执行频率
```typescript
useEffect(() => {
  console.log('Effect executed:', { originalFilePath, videoFile });
  // effect 逻辑
}, [originalFilePath, videoFile]);
```

#### 使用 React DevTools Profiler
- 监控组件重渲染频率
- 识别性能瓶颈
- 验证优化效果

## 总结

**关键要点：**
1. 🔑 **统一类型定义**：所有全局 API 类型只在 `src/preload/index.d.ts` 中定义
2. 🚫 **避免重复**：不要在多个文件中重复定义相同的类型
3. 📁 **清晰分层**：全局类型 vs 业务类型 vs 组件类型
4. 🔍 **类型安全**：严格的 TypeScript 配置和定期检查

遵循这些规则可以确保代码库的类型安全性和可维护性。

## Modal 组件样式规范

### 概述
为了确保应用中所有 Modal 组件的视觉一致性和用户体验，我们建立了统一的 Modal 样式系统。所有 Modal 相关的基础样式都集中在 `src/renderer/src/styles/antd-theme.css` 中管理。

### 样式架构

#### 1. 基础样式（全局）
所有 Modal 共享的基础样式定义在 `antd-theme.css` 中：
- 遮罩层样式（背景模糊、透明度）
- 容器样式（背景渐变、边框、阴影、圆角）
- 头部样式（背景、边框、装饰线）
- 主体和底部样式
- 关闭按钮样式
- 按钮统一样式（primary、default、dangerous）

#### 2. 变体样式（主题化）
通过 CSS 类名区分不同类型的 Modal：

```css
/* 删除确认 Modal */
.delete-modal

/* 字幕加载 Modal */
.subtitle-modal

/* 转换指南 Modal */
.conversion-guide-modal

/* 设置 Modal */
.settings-modal
```

每种变体都有独特的主题色彩：
- 删除确认：红色主题 (`rgba(255, 77, 79, *)`)
- 字幕加载：蓝色主题 (`rgba(102, 126, 234, *)`)
- 转换指南：天蓝色主题 (`rgba(24, 144, 255, *)`)
- 设置：紫色主题 (`rgba(139, 92, 246, *)`)

### 使用规范

#### 1. Modal 组件使用
```tsx
// ✅ 正确使用
<Modal
  title={<div className={styles.modalTitle}>标题</div>}
  className="delete-modal" // 使用统一的类名
  // ... 其他属性
>
  <div className={styles.modalContent}>
    {/* 内容 */}
  </div>
</Modal>

// ❌ 错误使用
<Modal
  className={styles.customModal} // 避免使用组件特有的样式类
>
```

#### 2. 样式文件组织
- **全局 Modal 样式**：`src/renderer/src/styles/antd-theme.css`
- **组件特有样式**：各组件的 `.module.css` 文件中只包含该组件特有的样式
- **避免重复**：不要在组件样式文件中重复定义 Modal 基础样式

#### 3. 类名命名规范
- 使用 kebab-case 命名：`delete-modal`、`subtitle-modal`
- 类名应该描述 Modal 的功能或用途
- 避免使用组件名作为类名前缀

### CSS 变量系统

#### Modal 专用变量
```css
:root {
  --modal-bg: linear-gradient(145deg, var(--darker-bg) 0%, rgba(26, 26, 26, 0.95) 100%);
  --modal-border: rgba(102, 126, 234, 0.2);
  --modal-shadow: 0 20px 60px rgba(0, 0, 0, 0.5), 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(102, 126, 234, 0.1);
  --modal-header-bg: rgba(102, 126, 234, 0.05);
  --modal-header-border: rgba(102, 126, 234, 0.2);
  --modal-footer-border: rgba(255, 255, 255, 0.08);
  --modal-close-bg: rgba(255, 255, 255, 0.05);
  --modal-close-hover-bg: rgba(255, 255, 255, 0.1);
}
```

### 响应式设计

Modal 样式包含完整的响应式支持：
- 移动端适配（宽度、内边距调整）
- 按钮尺寸适配
- 关闭按钮位置调整

### 最佳实践

#### 1. 新增 Modal 类型
1. 在 `antd-theme.css` 中添加新的变体样式
2. 选择合适的主题色彩
3. 在组件中使用对应的类名
4. 更新本文档

#### 2. 自定义样式
- 优先使用 CSS 变量进行主题定制
- 组件特有样式放在对应的 `.module.css` 文件中
- 避免使用 `!important`，除非必要

#### 3. 维护原则
- 保持样式的一致性和可维护性
- 定期检查和清理重复的样式代码
- 确保新增功能遵循现有的设计规范

### 文件结构
```
src/renderer/src/styles/
├── antd-theme.css          # Modal 基础样式和变体
├── components.css          # 其他组件样式
└── global.css             # 全局样式

src/renderer/src/components/
└── [ComponentName]/
    ├── ComponentName.tsx
    └── ComponentName.module.css  # 组件特有样式
```

### 注意事项
1. 修改 Modal 基础样式时要考虑对所有 Modal 的影响
2. 新增变体样式时要确保与现有设计风格一致
3. 测试不同屏幕尺寸下的显示效果
4. 保持代码的可读性和可维护性

## React Context 最佳实践

### 三文件分离模式

为了避免性能问题和循环依赖，我们采用三文件分离的 Context 组织模式：

#### 1. 类型定义文件 (`*-context.ts`)
```typescript
// src/renderer/src/contexts/subtitle-list-context.ts
import { createContext } from 'react'
import type { SubtitleItem } from '@types_/shared'

export interface SubtitleListContextType {
  // 状态
  subtitles: SubtitleItem[]
  showSubtitles: boolean
  currentSubtitleIndex: number
  
  // 操作方法
  handleSubtitleUpload: (file: File) => boolean
  toggleSubtitles: () => void
  getCurrentSubtitleIndex: (currentTime: number) => number
  getCurrentSubtitle: (currentTime: number) => SubtitleItem | null
  setAutoScrollEnabled: (enabled: boolean) => void
  setCurrentSubtitleIndex: (index: number) => void
  restoreSubtitles: (subtitles: SubtitleItem[], currentSubtitleIndex: number) => void
}

export const SubtitleListContext = createContext<SubtitleListContextType | null>(null)
```

#### 2. Provider 组件文件 (`*Context.tsx`)
```typescript
// src/renderer/src/contexts/SubtitleListContext.tsx
import React from 'react'
import { useSubtitleList } from '../hooks/useSubtitleList'
import { SubtitleListContext, type SubtitleListContextType } from './subtitle-list-context'

export function SubtitleListProvider({
  children
}: {
  children: React.ReactNode
}): React.JSX.Element {
  const value: SubtitleListContextType = useSubtitleList()

  return <SubtitleListContext.Provider value={value}>{children}</SubtitleListContext.Provider>
}
```

#### 3. 自定义 Hook 文件 (`use*Context.ts`)
```typescript
// src/renderer/src/hooks/useSubtitleListContext.ts
import { useContext } from 'react'
import {
  SubtitleListContext,
  type SubtitleListContextType
} from '../contexts/subtitle-list-context'

export function useSubtitleListContext(): SubtitleListContextType {
  const context = useContext(SubtitleListContext)
  if (!context) {
    throw new Error('useSubtitleListContext 必须在 SubtitleListProvider 内部使用')
  }

  return context
}
```

### Context 性能优化策略

#### 1. 避免屏蔽获取 (Prop Drilling)
- 使用 Context 直接在需要的组件中获取状态
- 减少不必要的 props 传递
- 提高组件的可维护性

#### 2. 合理的 Provider 层级
```typescript
// App.tsx - Provider 嵌套结构
<PlaybackSettingsProvider>
  <ShortcutProvider>
    <PlayingVideoProvider>
      <SubtitleListProvider>
        {/* 应用内容 */}
      </SubtitleListProvider>
    </PlayingVideoProvider>
  </ShortcutProvider>
</PlaybackSettingsProvider>
```

#### 3. Hook 内部使用 Context
```typescript
// 优化前：通过参数传递
const subtitleControl = useSubtitleControl({
  subtitlesLength: subtitles.length,
  getSubtitle: (index) => subtitles[index],
  getAllSubtitles: () => subtitles,
  // ... 其他参数
})

// 优化后：Hook 内部使用 Context
const subtitleControl = useSubtitleControl({
  currentSubtitleIndex,
  currentTime,
  isPlaying,
  isVideoLoaded,
  onSeek,
  onPause
})

// Hook 内部
export function useSubtitleControl(params) {
  const { subtitles } = useSubtitleListContext()
  // 直接使用 context 中的数据
}
```

### 组件优化示例

#### 优化前：通过 Props 传递
```typescript
// SidebarSection 接收 subtitles 作为 prop
<SidebarSection
  subtitles={subtitleList.subtitles}
  // ... 其他 props
/>
```

#### 优化后：直接使用 Context
```typescript
// SidebarSection 内部使用 context
export function SidebarSection(props) {
  const { subtitles } = useSubtitleListContext()
  // 直接使用 context 中的数据
}

// 调用时不需要传递 subtitles
<SidebarSection
  // ... 其他 props（不包括 subtitles）
/>
```

### 最佳实践总结

1. **三文件分离**：类型定义、Provider 组件、自定义 Hook 分别放在不同文件
2. **类型安全**：使用 TypeScript 确保 Context 类型安全
3. **错误处理**：在自定义 Hook 中检查 Context 是否存在
4. **性能优化**：让 Hook 直接使用 Context，减少参数传递
5. **组件解耦**：组件直接使用 Context，减少 props 依赖

### 命名规范

- Context 类型：`*ContextType`
- Context 实例：`*Context`
- Provider 组件：`*Provider`
- 自定义 Hook：`use*Context`

这种模式确保了代码的可维护性、性能和类型安全。

## 性能优化：解决 PlayPage 频繁重新渲染问题

### 🚨 问题描述

在播放视频时，PlayPage 组件出现频繁重新渲染的严重性能问题，导致：
- 每秒多次重新渲染
- 自动保存功能不稳定
- 用户界面卡顿
- 资源消耗过高

### 🔍 根本原因分析

1. **usePlayStateSaver 使用了 useVideoTime()**
   - `useVideoTime()` 订阅视频时间变化
   - 每当视频播放时间更新时都会触发组件重新渲染
   - 这是导致频繁重新渲染的主要原因

2. **usePlayStateInitializer 依赖过于广泛**
   - 依赖数组包含整个 context 对象
   - Context 对象的任何变化都会导致重新运行

3. **React.memo 使用不当**
   - 缺少精确的比较函数
   - 无法有效阻止不必要的重新渲染

### ✅ 解决方案

#### 1. **优化 usePlayStateSaver Hook**

**🔧 关键改进：使用 ref 而不是状态订阅**

```typescript
// ❌ 错误方式：会导致重新渲染
const currentTime = useVideoTime()
const duration = useVideoDuration()

// ✅ 正确方式：使用 ref，不会重新渲染
const currentTimeRef = useVideoTimeRef()
const { durationRef } = useVideoStateRefs()

// 在需要时从 ref 读取值
const currentTime = currentTimeRef.current
const duration = durationRef.current
```

**🎯 效果：** 完全消除了自动保存导致的重新渲染

#### 2. **优化 usePlayStateInitializer Hook**

**🔧 关键改进：减少依赖数组，使用稳定的 ref**

```typescript
// ❌ 错误方式：依赖过多
useEffect(() => {
  // ...
}, [playingVideoContext, subtitleListContext, savePlayStateRef, restoreVideoState])

// ✅ 正确方式：只监听必要的依赖
useEffect(() => {
  // ...
}, [
  playingVideoContext.originalFilePath,
  playingVideoContext.videoFile
])
```

#### 3. **优化 PlayPage 组件**

**🔧 关键改进：精确的 React.memo 比较**

```typescript
// ✅ 自定义比较函数
const PlayPageMemo = React.memo<PlayPageProps>(
  function PlayPage({ onBack }) {
    // 组件逻辑
  },
  (prevProps, nextProps) => {
    // 🎯 精确比较：只有当 onBack 函数真正改变时才重新渲染
    return prevProps.onBack === nextProps.onBack
  }
)
```

### 📋 最佳实践指南

#### 1. **Hook 设计原则**

- **使用 ref 而不是状态订阅**：对于频繁更新但不需要触发重新渲染的数据
- **精确的依赖数组**：只包含真正需要监听的值
- **稳定的函数引用**：使用 `useCallback` 缓存函数

#### 2. **组件优化原则**

- **React.memo + 自定义比较**：精确控制重新渲染条件
- **useMemo 缓存复杂对象**：避免每次渲染时重新创建对象
- **useCallback 缓存事件处理函数**：保持引用稳定

#### 3. **性能监控**

```typescript
// 开发模式下的性能监控
if (process.env.NODE_ENV === 'development') {
  console.log('🎬 PlayPage 渲染 - ', new Date().toLocaleTimeString())
}
```

### 🚀 优化效果

#### 性能提升对比

| 优化前 | 优化后 |
|--------|--------|
| 每秒 3-5 次重新渲染 | 基本无重新渲染 |
| 自动保存不稳定 | 自动保存稳定运行 |
| 界面卡顿 | 流畅运行 |
| CPU 占用高 | CPU 占用正常 |

#### 关键特性

✅ **自动保存功能稳定运行** - 不会被重新渲染影响  
✅ **播放性能优化** - 消除了播放时的卡顿  
✅ **资源消耗降低** - 减少了不必要的计算  
✅ **代码可维护性提升** - 清晰的依赖关系  

### 🔧 技术架构

#### Hook 责任分离

```
usePlayStateSaver (自动保存)
├── useVideoTimeRef (ref，不触发渲染)
├── useVideoStateRefs (ref，不触发渲染)
└── 定时器机制 (5秒间隔)

usePlayStateInitializer (状态初始化)
├── 精确依赖监听
├── ref 缓存函数引用
└── 避免重复初始化

PlayPage (主组件)
├── React.memo + 自定义比较
├── useCallback 缓存函数
└── useMemo 缓存对象
```

### 📖 代码示例

#### 高性能 Hook 模式

```typescript
export function useOptimizedHook() {
  // 🔧 使用 ref 存储频繁变化的值
  const valueRef = useRef()
  
  // 🔧 缓存函数，避免重新创建
  const stableFunction = useCallback(() => {
    // 从 ref 读取值，不触发重新渲染
    const currentValue = valueRef.current
    // 处理逻辑
  }, []) // 稳定的依赖数组
  
  // 🔧 精确的 useEffect 依赖
  useEffect(() => {
    // 只监听真正需要的变化
  }, [必要的依赖])
  
  return { stableFunction }
}
```

#### 高性能组件模式

```typescript
const OptimizedComponent = React.memo(
  function Component({ onAction }) {
    // 🔧 缓存复杂计算
    const memoizedValue = useMemo(() => {
      return computeExpensiveValue()
    }, [dependencies])
    
    // 🔧 缓存事件处理函数
    const handleEvent = useCallback(() => {
      onAction()
    }, [onAction])
    
    return <div onClick={handleEvent}>{memoizedValue}</div>
  },
  // 🔧 自定义比较函数
  (prevProps, nextProps) => {
    return prevProps.onAction === nextProps.onAction
  }
)
```

### 🔍 调试工具

#### 重新渲染检测

```typescript
// 添加到组件中检测重新渲染
const renderCount = useRef(0)
renderCount.current++
console.log(`组件渲染次数: ${renderCount.current}`)
```

#### 依赖变化监控

```typescript
// 监控 useEffect 依赖变化
useEffect(() => {
  console.log('依赖发生变化:', { dep1, dep2 })
}, [dep1, dep2])
```

### 🎯 核心原则

1. **避免不必要的重新渲染** - 使用 ref 存储频繁变化的数据
2. **精确的依赖管理** - 只监听真正需要的变化
3. **稳定的引用** - 使用 memo 和 callback 保持引用稳定
4. **性能监控** - 及时发现和解决性能问题

这套解决方案确保了 EchoLab 应用在播放视频时的高性能运行，为用户提供流畅的使用体验。

## 字幕显示问题修复方案

### 问题背景
在版本迭代过程中，字幕组件由于localStorage中保存的旧版本配置数据导致显示异常或完全不可见。

### 解决方案

#### 1. 配置验证和自动修复
在字幕组件初始化时添加配置验证逻辑：
- 验证位置参数范围（0-100%）
- 验证尺寸参数范围（200-1200px宽，60-400px高）  
- 验证背景类型有效性
- 自动清理无效配置并重置为默认值

```typescript
// src/renderer/src/components/VideoPlayer/Subtitle.tsx
const [subtitleState, setSubtitleState] = useState<SubtitleState>(() => {
  try {
    const saved = localStorage.getItem(SUBTITLE_STATE_KEY)
    if (saved) {
      const parsedState = JSON.parse(saved)
      
      // 验证配置有效性
      const isValidPosition = /* 验证逻辑 */
      const isValidSize = /* 验证逻辑 */
      const isValidBackgroundType = /* 验证逻辑 */
      
      if (!isValidPosition || !isValidSize || !isValidBackgroundType) {
        console.warn('检测到无效的字幕配置，已重置为默认配置')
        localStorage.removeItem(SUBTITLE_STATE_KEY)
        return DEFAULT_SUBTITLE_STATE
      }
      
      return { ...DEFAULT_SUBTITLE_STATE, ...parsedState }
    }
    return DEFAULT_SUBTITLE_STATE
  } catch (error) {
    localStorage.removeItem(SUBTITLE_STATE_KEY)
    return DEFAULT_SUBTITLE_STATE
  }
})
```

#### 2. 设置界面重置选项
在外观设置中提供用户友好的重置界面：
- 位置：`src/renderer/src/components/Settings/AppearanceSection.tsx`
- 功能：检测自定义设置、一键重置、操作指导
- 用户体验：清晰的状态提示和操作反馈

#### 3. 全局快捷键支持
添加快捷键 `Ctrl+Shift+R` 用于快速重置字幕设置：
- Hook：`src/renderer/src/hooks/useSubtitleReset.ts`
- 快捷键注册：使用全局快捷键管理器
- 智能启用：仅在有自定义设置时生效

#### 4. 多层防护机制
- **第一层**：组件初始化时的配置验证和自动修复
- **第二层**：设置界面的手动重置选项
- **第三层**：全局快捷键的快速重置
- **第四层**：快捷键配置的持久化管理

### 最佳实践

#### 数据持久化
1. **验证优先**：加载配置前先验证数据有效性
2. **容错处理**：解析失败时自动清理并使用默认配置
3. **版本兼容**：考虑配置结构变更时的向下兼容

#### 用户体验
1. **自动修复**：无需用户干预，自动处理无效配置
2. **多重选择**：提供设置界面和快捷键两种重置方式
3. **状态提示**：清晰地告知用户当前配置状态

#### 代码组织
1. **Hook复用**：将重置逻辑封装为可复用的Hook
2. **全局管理**：通过Context Provider启用全局功能
3. **类型安全**：使用TypeScript确保配置类型正确性

### 相关文件
- `src/renderer/src/components/VideoPlayer/Subtitle.tsx` - 字幕组件主文件
- `src/renderer/src/components/Settings/AppearanceSection.tsx` - 外观设置界面
- `src/renderer/src/hooks/useSubtitleReset.ts` - 字幕重置Hook
- `src/renderer/src/hooks/useShortcutManager.ts` - 快捷键配置
- `src/renderer/src/contexts/ShortcutContext.tsx` - 快捷键上下文

### 调试技巧
1. 查看控制台警告信息了解配置验证结果
2. 检查localStorage中的`echolab_subtitle_state`键
3. 使用快捷键测试重置功能是否正常工作
4. 验证设置界面的状态检测和重置按钮

### 未来改进
1. 添加配置版本号，支持平滑升级
2. 提供配置导入/导出功能
3. 增加更多字幕外观自定义选项
4. 考虑云端配置同步功能

# EchoLab 字幕系统响应式设计指南

## 概述

本指南总结了 EchoLab 项目中字幕系统的响应式设计实现，解决了在不同窗口尺寸下字幕显示问题。

## 主要改进

### 1. 字幕尺寸系统重构

**问题描述：**
- 原始实现使用固定像素值（600px × 120px）
- 在不同窗口尺寸下无法自适应
- 导致字幕显示不全或无法正确遮挡原视频字幕

**解决方案：**
- 将字幕尺寸从固定像素值改为相对百分比
- 默认尺寸：宽度60%，高度15%
- 支持范围：宽度20%-100%，高度5%-50%

### 2. 响应式布局优化

**CSS 改进：**
```css
.subtitleContainer {
  width: 60%; /* 默认宽度60% */
  height: 15%; /* 默认高度15% */
  min-width: 20%;
  min-height: 5%;
  max-width: 100%;
  max-height: 50%;
}
```

**媒体查询适配：**
- 超大屏幕（≥2560px，4K+）：优化超高分辨率显示，避免字幕过大
- 大屏幕（1440px-2559px，2K）：针对高分辨率显示器优化
- 中等屏幕（1024px-1439px）：标准桌面显示器适配
- 小屏幕（768px-1023px）：应用最小分辨率范围优化

### 3. 窗口大小变化处理

**自动调整机制：**
- 监听 `window.resize` 事件
- 自动检查字幕位置边界
- 必要时重新定位到有效范围内

**手动重置功能：**
- 新增重置按钮（↺图标）
- 一键恢复默认位置和大小
- 配合背景切换按钮，提供完整控制体验

## 技术实现要点

### 1. 百分比坐标系统

```typescript
interface SubtitleState {
  position: {
    x: number // 相对于容器的百分比位置 (0-100)
    y: number // 相对于容器的百分比位置 (0-100)
  }
  size: {
    width: number // 字幕区域宽度（相对于容器的百分比 0-100）
    height: number // 字幕区域高度（相对于容器的百分比 0-100）
  }
  backgroundType: BackgroundType
}
```

### 2. 边界检查算法

```typescript
const halfWidthPercent = prev.size.width / 2
const halfHeightPercent = prev.size.height / 2

const minX = halfWidthPercent
const maxX = 100 - halfWidthPercent
const minY = halfHeightPercent
const maxY = 100 - halfHeightPercent

const adjustedX = Math.max(minX, Math.min(maxX, prev.position.x))
const adjustedY = Math.max(minY, Math.min(maxY, prev.position.y))
```

### 3. 拖拽和调整大小的响应式处理

- 拖拽：基于百分比计算新位置
- 调整大小：转换像素变化为百分比变化
- 实时边界限制：确保字幕不会超出视频区域

## 最佳实践

### 1. 状态管理
- 使用 localStorage 持久化字幕配置
- 包含完整的验证机制
- 自动清理无效配置

### 2. 用户体验
- 提供可视化边框指示
- 悬停时显示控制按钮
- 拖拽和调整大小时的视觉反馈

### 3. 响应式设计原则
- 优先使用相对单位（百分比）
- 设置合理的最小/最大尺寸限制
- 不同设备类型的差异化处理

## 文件结构

```
src/renderer/src/components/VideoPlayer/
├── Subtitle.tsx              # 主组件逻辑
├── Subtitle.module.css       # 响应式样式
├── VideoPlayer.tsx           # 视频播放器容器
└── VideoPlayer.module.css    # 容器样式（字幕覆盖层）
```

## 分辨率支持策略

### 设计原则
1. **以768px为最小分辨率基准**
2. **采用渐进式增强策略**：从小屏幕向大屏幕逐步优化
3. **保持字幕可读性**：避免在大屏幕上字幕过小或过大

### 响应式断点
```css
/* 应用最小分辨率 */
768px - 1023px   → 小屏幕适配
1024px - 1439px  → 中等屏幕适配  
1440px - 2559px  → 大屏幕适配（2K）
2560px+          → 超大屏幕适配（4K+）
```

### 字体缩放策略
- **基础字体**：1.5rem → 1.8rem → 2.2rem
- **英文字幕**：1.75rem → 2rem → 2.5rem  
- **中文字幕**：1.4rem → 1.6rem → 2rem

## 后续优化建议

1. **性能优化**
   - 考虑使用 `ResizeObserver` 替代 `window.resize`
   - 添加防抖机制减少频繁计算
   - 优化大屏幕下的渲染性能

2. **功能扩展**
   - 添加字幕大小预设（小、中、大）
   - 支持字幕透明度调节
   - 记忆不同视频文件的字幕配置
   - 支持超宽屏（21:9）比例适配

3. **可访问性**
   - 添加键盘快捷键支持
   - 高对比度模式适配
   - 字幕字体大小的系统级响应
   - 支持缩放级别检测

## 注意事项

- 字幕容器的父元素（`.subtitleOverlay`）必须正确设置定位
- 百分比计算依赖于准确的父容器尺寸获取
- 在全屏切换时需要重新验证字幕位置边界

---

*此指南记录了 EchoLab 字幕系统响应式设计的核心实现，为后续开发和维护提供参考。*

# 字幕组件重构开发指南

## 概述

本文档总结了 SubtitleV2 组件的重构过程，将一个1742行的复杂组件拆分成多个专注、可维护的小组件和 hooks，提供了组件解耦的最佳实践。

## 重构原则

### 1. 单一职责原则
每个组件和 hook 都应该只负责一个特定的功能领域：
- **状态管理**：独立的 hook 管理特定状态
- **业务逻辑**：将复杂逻辑封装到专用的 hook 中
- **UI 展示**：组件只负责渲染，不包含复杂逻辑

### 2. 关注点分离
将不同的功能关注点分离到独立的模块中：
- **状态管理**：`useSubtitleState` - 管理字幕状态和本地存储
- **交互逻辑**：`useSubtitleDragAndResize` - 处理拖拽和调整大小
- **样式计算**：`useSubtitleStyles` - 动态样式和响应式计算
- **文本处理**：`subtitleTextUtils` - 文本分割和语言检测
- **UI 组件**：`SubtitleControls`、`SubtitleContent` - 专用UI组件

## 重构架构

### Hook 层
```typescript
// 状态管理
useSubtitleState() - 字幕状态、本地存储、切换操作

// 交互逻辑  
useSubtitleDragAndResize() - 拖拽、调整大小、验证边距

// 样式计算
useSubtitleStyles() - 动态字体大小、控制按钮尺寸、响应式布局
```

### 组件层
```typescript
// 主容器组件
SubtitleV3 - 整合所有 hooks，处理生命周期

// 功能子组件
SubtitleControls - 控制按钮（遮罩、背景、重置）
SubtitleContent - 内容渲染（多语言、多模式）
```

### 工具层
```typescript
// 文本处理工具
subtitleTextUtils - 中英文分割、可点击单词生成
```

## 最佳实践

### 1. Hook 设计模式

#### 状态管理 Hook
```typescript
export const useSubtitleState = (): {
  subtitleState: SubtitleMarginsState
  setSubtitleState: React.Dispatch<React.SetStateAction<SubtitleMarginsState>>
  updateSubtitleState: (newState: SubtitleMarginsState) => void
  toggleBackgroundType: () => void
  toggleMaskMode: () => void
  saveSubtitleState: (state: SubtitleMarginsState) => void
} => {
  // 实现细节...
}
```

**优势**：
- 明确的类型定义
- 封装的状态逻辑
- 自动本地存储
- 配置验证

#### 交互逻辑 Hook
```typescript
export const useSubtitleDragAndResize = (
  subtitleState: SubtitleMarginsState,
  updateSubtitleState: (state: SubtitleMarginsState) => void,
  getParentBounds: () => { width: number; height: number },
  currentLayout: { left: number; top: number; width: number; height: number }
) => {
  // 返回拖拽相关的状态和处理函数
}
```

**优势**：
- 依赖注入，易于测试
- 复杂交互逻辑的封装
- 可复用的交互模式

### 2. 组件拆分策略

#### 功能性组件
```typescript
interface SubtitleControlsProps {
  isMaskMode: boolean
  backgroundType: BackgroundType
  buttonSize: number
  iconSize: number
  onToggleMaskMode: () => void
  onToggleBackgroundType: () => void
  onReset: () => void
}

export const SubtitleControls: React.FC<SubtitleControlsProps> = ({...}) => {
  // 只负责渲染控制按钮，不包含业务逻辑
}
```

**优势**：
- 清晰的 props 接口
- 易于测试和调试
- 可独立开发和维护

#### 内容渲染组件
```typescript
interface SubtitleContentProps {
  currentSubtitle: SubtitleItem | null
  displayMode: DisplayMode
  dynamicTextStyle: React.CSSProperties
  dynamicEnglishTextStyle: React.CSSProperties
  dynamicChineseTextStyle: React.CSSProperties
  onWordHover: (isHovering: boolean) => void
  onWordClick: (word: string, event: React.MouseEvent) => void
}
```

**优势**：
- 专注于内容渲染
- 支持多种显示模式
- 样式外部注入，便于响应式

### 3. 工具函数设计

#### 文本处理工具
```typescript
// 语言检测
export const isChinese = (text: string): boolean => { ... }

// 智能分割
export const splitTextIntoWords = (
  text: string,
  onWordHover: (isHovering: boolean) => void,
  onWordClick: (word: string, event: React.MouseEvent) => void
): React.ReactNode[] => { ... }
```

**优势**：
- 纯函数，易于测试
- 可复用的文本处理逻辑
- 支持事件回调注入

### 4. TypeScript 类型设计

#### 完整的类型定义
```typescript
// 背景类型
export type BackgroundType = 'transparent' | 'blur' | 'solid-black' | 'solid-gray'

// 状态接口
export interface SubtitleMarginsState {
  margins: {
    left: number
    top: number
    right: number
    bottom: number
  }
  backgroundType: BackgroundType
  isMaskMode: boolean
  maskFrame: {
    left: number
    top: number
    width: number
    height: number
  }
}
```

**优势**：
- 类型安全
- 清晰的数据结构
- IDE 智能提示

## 代码质量改进

### 重构前问题
1. **单一组件过大**：1742行代码，难以维护
2. **职责混乱**：状态管理、UI渲染、交互逻辑混在一起
3. **可测试性差**：复杂的组件难以进行单元测试
4. **可复用性低**：功能耦合，无法独立复用

### 重构后优势
1. **代码模块化**：每个文件专注单一职责
2. **易于维护**：修改某个功能不影响其他部分
3. **便于测试**：可独立测试每个 hook 和组件
4. **高可复用性**：hooks 和组件可在其他场景复用

## 性能优化

### 1. memoization 策略
- 使用 `useMemo` 缓存计算结果
- 使用 `useCallback` 避免函数重新创建
- 合理使用 `React.memo` 包装子组件

### 2. 状态更新优化
- 避免不必要的状态更新
- 使用函数式更新避免闭包陷阱
- 批量更新状态以减少重渲染

### 3. 事件处理优化
- 事件委托减少事件监听器数量
- 及时清理事件监听器
- 使用 passive 监听器提升性能

## 维护指南

### 1. 添加新功能
1. 评估功能应该放在哪个层次（Hook/组件/工具）
2. 如果是新的状态，考虑扩展现有 Hook 或创建新 Hook
3. 如果是新的 UI，创建独立的功能组件
4. 保持单一职责原则

### 2. 修复问题
1. 定位问题所在的模块
2. 在对应的 Hook 或组件中修复
3. 确保修改不影响其他模块
4. 添加相应的类型和错误处理

### 3. 性能调优
1. 使用 React DevTools 分析性能瓶颈
2. 针对具体的 Hook 或组件进行优化
3. 考虑懒加载和代码分割

## 总结

通过将庞大的 SubtitleV2 组件拆分成多个专注的 hooks 和组件，我们实现了：

1. **更好的代码组织**：每个文件都有明确的职责
2. **更高的可维护性**：修改和扩展功能变得更容易
3. **更强的可测试性**：可以独立测试每个部分
4. **更好的可复用性**：hooks 和组件可在其他地方复用
5. **更清晰的依赖关系**：组件间的依赖关系变得明确

这种重构方法可以作为处理其他复杂组件的参考模式。

## 定位框自动调整功能

### 功能概述
定位框（MaskFrame）现在具备智能自动调整功能，能够根据视频的显示宽高比（DAR）和窗口大小变化，始终保持对视频区域的准确框选。

### 核心实现

#### 1. 视频显示区域计算
定位框会根据以下因素自动计算视频在容器中的实际显示区域：
- 视频的原始宽高比（DAR）
- 容器的实际尺寸
- `object-fit: contain` 的布局规则

#### 2. 自动调整机制
- **窗口调整监听**：使用 `resize` 事件监听窗口大小变化
- **智能判断**：只有在用户未手动操作过定位框时才自动调整
- **防抖处理**：100ms 防抖避免频繁计算
- **阈值检查**：只有当差异超过 2% 时才进行调整

#### 3. 交互状态管理
```typescript
const [hasUserInteracted, setHasUserInteracted] = useState(false)
```
- 用户拖拽或调整大小时，标记为已交互状态
- 已交互状态下不会自动调整，保持用户的自定义设置
- 提供重置功能，可恢复自动调整

#### 4. 重置功能
- **重置按钮**：鼠标悬停时显示"重置到视频"按钮
- **功能**：
  - 重新计算基于当前视频DAR的定位框位置
  - 重置交互状态，重新启用自动调整
  - 确保定位框始终框选视频区域

### 技术细节

#### 计算逻辑
```typescript
const calculateVideoDisplayArea = (
  displayAspectRatio: number,
  containerWidth: number,
  containerHeight: number
) => {
  const containerAspectRatio = containerWidth / containerHeight
  
  if (displayAspectRatio > containerAspectRatio) {
    // 视频更宽：以容器宽度为准
    videoDisplayWidth = containerWidth
    videoDisplayHeight = containerWidth / displayAspectRatio
  } else {
    // 视频更高：以容器高度为准
    videoDisplayHeight = containerHeight
    videoDisplayWidth = containerHeight * displayAspectRatio
  }
  
  // 转换为百分比返回
}
```

#### 防抖实现
```typescript
let timeoutId: NodeJS.Timeout
const debouncedHandleResize = (): void => {
  clearTimeout(timeoutId)
  timeoutId = setTimeout(handleResize, 100)
}
```

### 用户体验改进

#### 预期行为
1. **初次加载**：定位框自动适配视频区域
2. **窗口调整**：定位框自动跟随视频位置变化
3. **用户操作**：用户拖拽或调整后，保持用户设置不变
4. **重置需求**：用户可随时点击重置按钮恢复自动模式

#### 交互反馈
- 悬停时显示边框高亮
- 拖拽时改变鼠标样式
- 重置按钮有悬停效果
- 控制台提供详细日志用于调试

### 兼容性说明
- 兼容不同视频宽高比（16:9、4:3、21:9等）
- 支持窗口大小实时调整
- 保持与现有字幕系统的完全兼容
- 向后兼容旧版配置数据

### 调试功能
开发模式下提供详细日志：
```typescript
console.log('🔧 窗口大小变化，自动调整定位框以框选视频...')
console.log('📐 计算的视频区域:', videoArea)
console.log('📐 当前定位框:', maskFrame)
```

### 性能优化
- 使用防抖减少计算频率
- 只在必要时更新状态
- 避免不必要的重渲染
- 高效的百分比计算避免像素级精确度带来的性能损耗

## 性能优化最佳实践

### 1. 问题识别模式

#### 1.1 频繁重新渲染识别
```typescript
// 在组件中添加渲染日志来诊断问题
RendererLogger.componentRender({
  component: 'ComponentName',
  props: {
    // 只记录关键props，避免记录频繁变化的值
    staticProp: someValue,
    // ❌ 不要记录: currentTime, callbacks, 大对象
    // ✅ 记录: 状态变化、关键配置
  }
})
```

#### 1.2 性能问题信号
- 控制台日志显示每秒多次组件渲染
- 用户交互时出现卡顿
- 拖拽或鼠标移动时CPU使用率过高
- React DevTools Profiler显示异常的render频率

### 2. 时间相关组件优化策略

#### 2.1 避免时间属性传递
```typescript
// ❌ 错误做法：通过props传递频繁变化的时间
interface ComponentProps {
  currentTime: number  // 每秒变化多次
  duration: number    // 可能变化
}

// ✅ 正确做法：组件内部使用hooks获取时间
function Component() {
  const { currentTimeRef, durationRef } = useVideoStateRefs()
  // 直接从refs获取最新值，不触发重新渲染
}
```

#### 2.2 memo比较函数优化
```typescript
// VideoPlayer组件的优化示例
const arePropsEqual = (prevProps: Props, nextProps: Props): boolean => {
  // 1. 只比较影响UI的属性
  if (prevProps.isPlaying !== nextProps.isPlaying) return false
  if (prevProps.volume !== nextProps.volume) return false
  
  // 2. 字幕内容变化比较（只比较关键属性）
  if (prevProps.currentSubtitle?.startTime !== nextProps.currentSubtitle?.startTime) return false
  
  // 🚫 不比较频繁变化的时间属性
  // currentTime 和 duration 在播放过程中会频繁变化
  // 但不影响 VideoPlayer 组件的 UI 渲染
  
  return true
}
```

### 3. 节流机制实现

#### 3.1 60fps节流更新
```typescript
const useThrottledUpdate = () => {
  const lastUpdateTimeRef = useRef(0)
  const pendingUpdateRef = useRef(false)
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const throttledUpdate = useCallback((updateFn: () => void) => {
    const now = Date.now()
    const timeSinceLastUpdate = now - lastUpdateTimeRef.current

    if (timeSinceLastUpdate >= 16) { // 60fps = 16ms间隔
      updateFn()
      lastUpdateTimeRef.current = now
      pendingUpdateRef.current = false
    } else {
      if (!pendingUpdateRef.current) {
        pendingUpdateRef.current = true
        updateTimeoutRef.current = setTimeout(() => {
          updateFn()
          lastUpdateTimeRef.current = Date.now()
          pendingUpdateRef.current = false
        }, 16 - timeSinceLastUpdate)
      }
    }
  }, [])

  return { throttledUpdate }
}
```

#### 3.2 鼠标移动节流
```typescript
const handleMouseMove = useCallback((): void => {
  const now = Date.now()
  const timeSinceLastMove = now - lastMouseMoveTimeRef.current

  // 状态检查 + 时间节流
  if (showControls && timeSinceLastMove < 100) {
    return // 已显示且时间间隔太短，跳过
  }

  lastMouseMoveTimeRef.current = now

  // 只有在控制栏未显示时才更新状态
  if (!showControls) {
    setShowControls(true)
  }
}, [showControls])
```

### 4. 组件架构优化

#### 4.1 Props分离策略
```typescript
// ❌ 错误：将所有数据都通过props传递
<VideoControls 
  currentTime={currentTime}  // 频繁变化
  duration={duration}        // 可能变化
  onSeek={onSeek}           // 函数引用
  // ... 其他props
/>

// ✅ 正确：分离静态配置和动态数据
<VideoControls 
  isPlaying={isPlaying}     // 只在需要时变化
  volume={volume}           // 只在需要时变化
  // currentTime 通过内部hooks获取
/>
```

#### 4.2 条件渲染优化
```typescript
// ✅ 组合渲染条件，减少不必要的组件创建
{isFullscreen && showControls && (
  <VideoControlsFullScreen {...props} />
)}

// 而不是在组件内部判断显示状态
```

### 5. 状态检查优化

#### 5.1 避免无效状态更新
```typescript
// ✅ 在状态更新前检查当前状态
const updateState = useCallback((newValue: boolean) => {
  if (currentState !== newValue) {  // 只在值真正改变时更新
    setCurrentState(newValue)
  }
}, [currentState])
```

#### 5.2 合并相关状态更新
```typescript
// ✅ 使用单一状态对象管理相关状态
const [controlsState, setControlsState] = useState({
  showControls: false,
  isUserInteracting: false,
  isPausedByHover: false
})

// 批量更新
setControlsState(prev => ({
  ...prev,
  showControls: true,
  isUserInteracting: true
}))
```

### 6. 开发检查清单

#### 6.1 组件渲染优化检查
- [ ] **时间依赖检查**: 组件是否依赖频繁变化的时间属性?
- [ ] **memo比较函数**: 是否只比较影响UI的关键属性?
- [ ] **条件渲染**: 是否避免了创建不必要的组件实例?
- [ ] **hooks使用**: 是否合理使用refs避免不必要的重新渲染?

#### 6.2 交互性能检查
- [ ] **鼠标事件节流**: 是否对高频鼠标事件进行了节流处理?
- [ ] **状态更新检查**: 是否在更新前检查状态值是否真正改变?
- [ ] **批量更新**: 是否将相关的状态更新进行了批量处理?
- [ ] **清理机制**: 是否正确清理了定时器和事件监听器?

#### 6.3 拖拽性能检查
- [ ] **60fps节流**: 拖拽更新是否限制在60fps以内?
- [ ] **refs使用**: 是否使用refs存储不影响渲染的数据?
- [ ] **立即应用**: 拖拽结束时是否立即应用待处理的更新?
- [ ] **边界检查**: 是否正确处理了拖拽边界和异常情况?

#### 6.4 memo组件检查
- [ ] **比较函数粒度**: 比较函数是否足够精细和准确?
- [ ] **属性分组**: 是否将相关属性分组进行比较?
- [ ] **跳过昂贵比较**: 是否跳过了函数引用和大对象的比较?
- [ ] **文档注释**: 比较逻辑是否有清晰的注释说明?

### 7. 性能监控

#### 7.1 开发时监控
```typescript
// 使用React DevTools Profiler
// 设置performance.mark()标记关键操作
performance.mark('drag-start')
// ... 拖拽操作
performance.mark('drag-end')
performance.measure('drag-duration', 'drag-start', 'drag-end')
```

#### 7.2 渲染计数监控
```typescript
// 开发环境下的渲染计数器
const renderCountRef = useRef(0)
renderCountRef.current++

if (process.env.NODE_ENV === 'development') {
  console.log(`${componentName} rendered ${renderCountRef.current} times`)
}
```

### 8. 最新优化案例：VideoPlayer组件完全优化

#### 8.1 问题描述
VideoPlayer组件在视频播放期间频繁重新渲染，因为memo比较函数对currentTime和duration变化敏感。

#### 8.2 解决方案
```typescript
// 1. 完全移除时间属性依赖
const arePropsEqual = (prevProps: Props, nextProps: Props): boolean => {
  // 只比较真正影响UI的属性
  if (prevProps.videoFile !== nextProps.videoFile) return false
  if (prevProps.isPlaying !== nextProps.isPlaying) return false
  
  // 🚫 完全移除对 currentTime 和 duration 的比较
  // ReactPlayer 内部会处理播放进度
  // VideoProgressBar 通过hooks独立获取时间数据
  
  return true
}

// 2. 子组件内部使用hooks获取时间数据
function VideoProgressBar() {
  const { currentTimeRef, durationRef } = useVideoStateRefs()
  // 不再通过props接收时间数据
}

// 3. 条件渲染优化
{isFullscreen && showControls && (
  <VideoControlsFullScreen {...staticProps} />
)}
```

#### 8.3 优化效果
- **播放期间零重新渲染**: currentTime变化不再触发VideoPlayer重新渲染
- **性能大幅提升**: CPU使用率显著降低
- **功能完全保留**: 所有播放控制功能正常工作

### 9. 性能优化原则总结

1. **数据流设计**: 频繁变化的数据通过refs而非props传递
2. **组件职责分离**: 让每个组件只关心自己需要的数据
3. **渲染条件优化**: 在组件外层进行条件判断，避免内部无效渲染
4. **memo比较精准**: 只比较真正影响UI的关键属性
5. **hooks合理使用**: 用useRef存储不触发渲染的数据，用useState管理UI状态

通过这些优化策略，可以实现理想的性能目标：
- **拖拽操作**: 稳定60fps
- **鼠标交互**: <100ms响应时间  
- **组件渲染**: 播放期间<10次/秒，理想情况下接近0次/秒
- **内存使用**: 无内存泄漏，及时清理资源

## 性能优化指南

### 字幕组件渲染优化

#### 优化策略
为了提升 VideoPlayer 组件的渲染性能，我们采用了以下策略：

1. **组件拆分**: 将字幕显示逻辑拆分为独立的细粒度组件
2. **精确渲染控制**: 使用 React.memo 和自定义比较函数
3. **渲染范围控制**: 确保只有字幕内容变化时才重新渲染

#### 已实现的优化组件

##### 1. 字幕文本组件
- `OriginalSubtitleText`: 原始字幕文本组件
- `ChineseSubtitleText`: 中文字幕文本组件  
- `EnglishSubtitleText`: 英文字幕文本组件
- `BilingualSubtitleLine`: 双语字幕行组件
- `SubtitlePlaceholder`: 占位符组件

##### 2. 性能优化特性
- **React.memo**: 所有组件都使用 React.memo 包装
- **自定义比较函数**: 只比较关键属性（text、style、className）
- **渲染日志**: 集成 RendererLogger 监控组件渲染频率

##### 3. 优化效果
通过组件拆分和精确渲染控制，实现了：
- 字幕文本只在内容变化时渲染
- 减少 VideoPlayer 主组件的重新渲染
- 提升播放过程中的性能表现

##### 4. 时间检查优化
为避免频繁重新渲染，采用了智能的时间检查机制：
- **使用 `useVideoTimeRef`**: 获取时间引用而不触发重新渲染
- **字幕范围检查**: 只有当时间超出当前字幕范围时才检查是否需要切换
- **定时检查机制**: 每 100ms 检查一次，平衡性能和响应速度
- **精确比较**: 使用 `startTime` 和 `text` 来判断是否是同一条字幕

```tsx
// 智能字幕更新检查
const checkSubtitleUpdate = useCallback((): void => {
  const currentTime = currentTimeRef.current
  
  // 只有超出当前字幕时间范围才检查更新
  const needsUpdate = !currentSubtitle || 
    currentTime < currentSubtitle.startTime || 
    currentTime > currentSubtitle.endTime
    
  if (needsUpdate) {
    // 检查新字幕是否真的不同
    const newSubtitle = subtitleListContext.getCurrentSubtitle(currentTime)
    const isSameSubtitle = currentSubtitle && newSubtitle &&
      currentSubtitle.startTime === newSubtitle.startTime &&
      currentSubtitle.text === newSubtitle.text
      
    if (!isSameSubtitle) {
      setCurrentSubtitle(newSubtitle) // 只有真正变化时才更新
    }
  }
}, [currentSubtitle, subtitleListContext, displayMode])
```

#### 使用示例

```tsx
// 在 SubtitleContent 中使用优化组件
switch (displayMode) {
  case 'chinese':
    return (
      <ChineseSubtitleText
        text={chineseText}
        style={dynamicChineseTextStyle}
        onWordHover={onWordHover}
        onWordClick={onWordClick}
      />
    )
}
```

#### 性能监控
通过控制台查看组件渲染日志，监控优化效果：
- 观察字幕组件的渲染频率
- 确认只在文本内容变化时才重新渲染
- 验证播放过程中的性能改进

#### 最佳实践
1. **细粒度组件**: 优先创建职责单一的小组件
2. **合理使用 memo**: 为经常重新渲染的组件添加 memo 优化
3. **自定义比较**: 为复杂组件编写精确的比较函数
4. **渲染监控**: 使用日志监控组件渲染性能

## 代码规范

### 组件性能优化规范
- 对于频繁重新渲染的组件，必须使用 React.memo
- 自定义比较函数应该只比较影响渲染的关键属性
- 所有优化组件都应该添加 displayName 以便调试
- 重要组件应该添加渲染日志进行性能监控

## 核心架构

### 字幕状态管理 - 最新重构 (v2024.12)

**重构说明**：字幕状态管理已从独立的 `useSubtitleState` 迁移到 `useVideoPlaybackSettings` 中进行统一管理。

#### 新的架构设计

```typescript
// 统一的播放设置管理 - 包含字幕状态
const playbackSettings = useVideoPlaybackSettings()

// 字幕相关操作
playbackSettings.updateSubtitleMargins(margins)
playbackSettings.updateSubtitleBackgroundType('blur')
playbackSettings.toggleBackgroundType()
playbackSettings.toggleMaskMode(width, height, aspectRatio)

// 获取字幕状态
const subtitleDisplay = playbackSettings.getSubtitleDisplay()
const margins = playbackSettings.getSubtitleMargins()
const backgroundType = playbackSettings.getSubtitleBackgroundType()
```

#### 迁移指南

**旧的用法**：
```typescript
// ❌ 旧版本 - 分离的状态管理
import { useSubtitleState } from '@renderer/hooks/useSubtitleState'

const { subtitleState, updateSubtitleState, toggleBackgroundType } = 
  useSubtitleState(width, height, aspectRatio)
```

**新的用法**：
```typescript
// ✅ 新版本 - 统一的播放设置管理
import { useVideoPlaybackSettings } from '@renderer/hooks/useVideoPlaybackSettings'

const playbackSettings = useVideoPlaybackSettings()

// 直接使用字幕相关方法
playbackSettings.updateSubtitleMargins(newMargins)
playbackSettings.toggleBackgroundType()

// 或使用兼容性层
import { useSubtitleDisplaySettings } from '@renderer/hooks/useSubtitleState'
const subtitleSettings = useSubtitleDisplaySettings(width, height, aspectRatio)
```

#### 类型定义

```typescript
// 字幕显示设置 - 存储在播放设置中
interface SubtitleDisplaySettings {
  margins: SubtitleMargins
  backgroundType: BackgroundType
  isMaskMode: boolean
  maskFrame: MaskFrame
}

// 视频播放设置 - 包含字幕设置
interface VideoPlaybackSettings {
  displayMode: 'none' | 'original' | 'chinese' | 'english' | 'bilingual'
  volume: number
  playbackRate: number
  isSingleLoop: boolean
  isAutoPause: boolean
  subtitleDisplay?: SubtitleDisplaySettings // 字幕显示配置
}
```

#### 数据存储

- **全局设置**：存储在 `StoreSettings.playback` 中
- **视频特定设置**：存储在 `RecentPlayItem.videoPlaybackSettings` 中
- **字幕设置**：作为 `VideoPlaybackSettings.subtitleDisplay` 的一部分

#### 兼容性

为保持向后兼容，保留了 `useSubtitleState` hook，但它现在作为兼容性层委托给 `useVideoPlaybackSettings`：

```typescript
// 兼容性层 - 逐步迁移
export const useSubtitleState = (width, height, aspectRatio) => {
  const playbackSettings = useVideoPlaybackSettings()
  // ... 将调用委托给 playbackSettings
}

// 推荐的新 API
export const useSubtitleDisplaySettings = (width, height, aspectRatio) => {
  // 提供更清晰的字幕设置接口
}
```

### 技术栈

- **主进程**: Electron + Node.js
- **渲染进程**: React 18 + TypeScript + Vite
- **状态管理**: React Context + Hooks
- **数据持久化**: electron-store
- **样式**: CSS Modules + CSS 变量
- **构建**: electron-builder

### 项目结构

```
src/
├── main/           # 主进程代码
├── preload/        # 预加载脚本
├── renderer/       # 渲染进程代码
│   ├── src/
│   │   ├── components/  # React 组件
│   │   ├── hooks/      # 自定义 Hooks
│   │   ├── contexts/   # React Context
│   │   ├── types/      # TypeScript 类型
│   │   ├── utils/      # 工具函数
│   │   └── styles/     # 样式文件
│   └── ...
└── types/          # 共享类型定义
```

## 核心功能模块

### 1. 视频播放管理

**主要组件**：
- `VideoPlayer` - 核心视频播放器
- `VideoControls` - 播放控制器
- `VideoSection` - 视频区域容器

**核心 Hooks**：
- `useVideoPlayerContext` - 视频播放器状态
- `useVideoPlaybackSettings` - 播放设置管理（包含字幕）
- `useVideoPlayerHooks` - 播放器操作集合

### 2. 字幕系统

**主要组件**：
- `SubtitleV3` - 字幕显示组件
- `SubtitleControls` - 字幕控制器
- `MaskFrame` - 字幕定位框

**核心 Hooks**：
- `useVideoPlaybackSettings` - 统一的字幕状态管理
- `useSubtitleDisplaySettings` - 现代化字幕设置接口
- `useSubtitleState` - 兼容性层（逐步废弃）

### 3. 播放列表管理

**主要组件**：
- `RecentPlayList` - 最近播放列表
- `PlayListItem` - 播放项组件

**核心 Hooks**：
- `useRecentPlayList` - 播放列表管理
- `usePlayingVideoContext` - 当前播放视频状态

## 开发规范

### 代码组织

1. **组件命名**：使用 PascalCase，文件名与组件名一致
2. **Hook 命名**：使用 camelCase，以 `use` 开头
3. **类型定义**：使用 PascalCase，接口以 `I` 开头或直接使用描述性名称
4. **文件结构**：每个组件一个文件夹，包含 `.tsx`、`.module.css` 和必要的子组件

### 状态管理模式

```typescript
// Context Provider 模式
export function VideoPlaybackSettingsProvider({ children }) {
  const value = useVideoPlaybackSettings()
  return (
    <VideoPlaybackSettingsContext.Provider value={value}>
      {children}
    </VideoPlaybackSettingsContext.Provider>
  )
}

// Hook 消费模式
export function useVideoPlaybackSettingsContext() {
  const context = useContext(VideoPlaybackSettingsContext)
  if (!context) {
    throw new Error('useVideoPlaybackSettingsContext must be used within a VideoPlaybackSettingsProvider')
  }
  return context
}
```

### 性能优化策略

1. **memo 优化**：对于频繁渲染的组件使用 `React.memo`
2. **回调稳定性**：使用 `useCallback` 和 `useMemo` 避免不必要的重渲染
3. **状态分离**：将不同关注点的状态分离到不同的 Context 中
4. **懒加载**：对于非关键路径的组件使用 `React.lazy`

### 错误处理

```typescript
// 统一错误处理模式
try {
  const result = await someAsyncOperation()
  // 处理成功情况
} catch (error) {
  console.error('操作失败:', error)
  // 用户友好的错误提示
}
```

## API 设计原则

### Hook 设计

1. **单一职责**：每个 Hook 只负责一个特定的功能域
2. **返回值一致性**：返回对象包含状态和操作方法
3. **类型安全**：完整的 TypeScript 类型定义
4. **向后兼容**：提供兼容性层支持渐进式迁移

### 组件接口

```typescript
interface ComponentProps {
  // 必需属性
  data: DataType
  onAction: (item: DataType) => void
  
  // 可选属性
  className?: string
  style?: React.CSSProperties
  
  // 回调函数
  onError?: (error: Error) => void
}
```

## 测试策略

### 单元测试

- 使用 Vitest 进行单元测试
- 重点测试 Hook 和工具函数
- 模拟外部依赖

### 集成测试

- 测试组件间的交互
- 验证数据流的正确性
- 测试错误边界

## 构建和部署

### 开发环境

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 类型检查
pnpm type-check
```

### 生产构建

```bash
# 构建应用
pnpm build

# 打包分发版本
pnpm dist
```

## 常见问题和解决方案

### 字幕状态迁移

**问题**：从旧版 `useSubtitleState` 迁移到新版 `useVideoPlaybackSettings`

**解决方案**：
1. 使用兼容性层进行渐进式迁移
2. 新组件直接使用 `useVideoPlaybackSettings` 或 `useSubtitleDisplaySettings`
3. 旧组件可继续使用 `useSubtitleState`，它会自动委托给新的管理系统

### 性能优化

**问题**：组件频繁重渲染

**解决方案**：
1. 使用 `React.memo` 包装纯组件
2. 使用 `useCallback` 稳定函数引用
3. 使用 `useMemo` 缓存计算结果
4. 检查依赖数组，避免不必要的依赖

### 类型安全

**问题**：TypeScript 类型错误

**解决方案**：
1. 定义完整的接口类型
2. 使用泛型约束
3. 利用类型守卫进行运行时检查
4. 使用严格的 tsconfig 配置

## 最佳实践

### 1. 组件设计

- **单一职责原则**：每个组件只负责一个特定功能
- **组合优于继承**：使用组合模式构建复杂组件
- **Props 接口设计**：清晰、类型安全的 Props 定义

### 2. 状态管理

- **就近原则**：状态尽可能接近使用它的组件
- **状态提升**：共享状态提升到最近的公共父组件
- **Context 使用**：仅用于真正需要跨多层组件传递的状态

### 3. 性能考虑

- **避免在渲染时创建对象**：使用 `useMemo` 和 `useCallback`
- **条件渲染优化**：使用 `&&` 操作符进行条件渲染
- **列表渲染优化**：提供稳定的 `key` 属性

## 更新日志

### v2024.12 - 字幕状态管理重构

- ✅ 将字幕状态管理集成到 `useVideoPlaybackSettings`
- ✅ 提供 `useSubtitleDisplaySettings` 现代化接口
- ✅ 保持 `useSubtitleState` 兼容性层
- ✅ 统一数据存储到播放设置中
- ✅ 更新类型定义和文档

### 未来计划

- [ ] 完全迁移所有组件到新的字幕状态管理
- [ ] 优化播放设置的数据库存储结构
- [ ] 增强字幕编辑功能
- [ ] 添加更多字幕显示选项

# EchoLab 主题系统优化指南

## 概述

本项目已完成从传统 CSS 覆盖方式向现代设计令牌（Design Token）系统的全面迁移，遵循 Ant Design 5.x 设计系统规范。该优化涉及 18 个 CSS 模块文件，总计超过 2000 行代码的现代化改造。

### 核心优化原则

#### 1. 设计令牌优先原则
- **废弃**: 自定义 CSS 变量 (`--dark-bg`, `--text-primary`, `--accent-color`)
- **采用**: Ant Design CSS 变量系统
  ```css
  /* 旧方式 */
  background-color: var(--dark-bg);
  color: var(--text-primary);
  
  /* 新方式 */
  background-color: var(--ant-color-bg-layout);
  color: var(--ant-color-text);
  ```

#### 2. 消除 !important 声明
- **目标**: 完全移除所有不必要的 `!important` 声明
- **方法**: 通过正确的 CSS 特异性和设计令牌实现样式优先级
- **成果**: 消除了数百个 `!important` 声明，减少样式冲突

#### 3. 标准化动画系统
```css
/* 统一使用 Ant Design 动画令牌 */
transition: all var(--ant-motion-duration-mid) var(--ant-motion-ease-out);
```

### 关键文件优化记录

#### 核心应用文件
- **App.module.css** (154行): 布局变量转换，主内容区域样式优化
- **VideoPlayer.module.css** (97行): 背景色和动画变量更新
- **UserInputContainer.module.css** (51行): 完全移除 `!important` 声明

#### 界面组件文件
- **AppHeader.module.css** (92行): 消除 `!important` 使用，现代化过渡效果
- **SidebarSection.module.css** (34行): 背景变量标准化
- **Settings.module.css** (232行): 大规模清理，移除大量 `!important` 使用

#### 核心功能组件
- **WordCard.module.css** (279行): 全面现代化，移除所有 `!important` 声明
- **HomePage.module.css** (440行): 大规模优化，清理大量 `!important` 声明
- **Subtitle.module.css** (506行): 最复杂的优化，字幕系统全面现代化

### 设计令牌映射表

#### 颜色系统
```css
/* 背景色 */
--dark-bg → --ant-color-bg-layout
--card-bg → --ant-color-bg-container
--border-color → --ant-color-border

/* 文本色 */
--text-primary → --ant-color-text
--text-secondary → --ant-color-text-secondary
--text-disabled → --ant-color-text-disabled

/* 主题色 */
--accent-color → --ant-color-primary
--success-color → --ant-color-success
--warning-color → --ant-color-warning
--error-color → --ant-color-error
```

#### 尺寸系统
```css
/* 间距 */
--spacing-sm → --ant-size-sm
--spacing-md → --ant-size
--spacing-lg → --ant-size-lg

/* 圆角 */
--border-radius → --ant-border-radius
```

#### 动画系统
```css
/* 持续时间 */
--transition-fast → --ant-motion-duration-fast
--transition-mid → --ant-motion-duration-mid
--transition-slow → --ant-motion-duration-slow

/* 缓动函数 */
--ease-out → --ant-motion-ease-out
--ease-in-out → --ant-motion-ease-in-out
```

### 开发最佳实践

#### 1. CSS 变量使用规范
```css
/* ✅ 正确做法 */
.component {
  background-color: var(--ant-color-bg-container);
  border: 1px solid var(--ant-color-border);
  border-radius: var(--ant-border-radius);
}

/* ❌ 避免做法 */
.component {
  background-color: #1f1f1f !important;
  border: 1px solid #333 !important;
}
```

#### 2. 响应式设计原则
- **最小分辨率要求**：768px 宽度，不支持更小的移动设备
- 使用 Ant Design 断点系统（从 `sm` 开始，移除 `xs`）
- 避免硬编码像素值
- 优先使用相对单位（rem、em、%）
- 所有媒体查询必须包含 `min-width: 768px`

#### 3. 动画和过渡效果
```css
/* 标准过渡效果 */
.interactive-element {
  transition: all var(--ant-motion-duration-mid) var(--ant-motion-ease-out);
}

/* 悬停效果 */
.button:hover {
  background-color: var(--ant-color-primary-hover);
  transform: translateY(-1px);
}
```

### 性能优化成果

#### 1. CSS 特异性优化
- 移除了数百个 `!important` 声明
- 减少了样式冲突和覆盖问题
- 提升了渲染性能

#### 2. 维护性提升
- 统一的设计令牌系统
- 更好的主题切换支持
- 代码可读性显著提升

#### 3. 文件大小优化
- 从 43KB 的问题样式代码优化为清洁的设计令牌样式
- 减少了冗余 CSS 规则
- 提高了样式加载效率

### 后续开发指导

#### 1. 新组件开发
- 必须使用 Ant Design 设计令牌
- 禁止使用 `!important` 声明（除非绝对必要）
- 遵循现有的命名约定和结构

#### 2. 样式调试
- 使用浏览器开发工具检查 CSS 变量值
- 验证设计令牌是否正确应用
- 确保在不同主题下的表现一致

#### 3. 主题扩展
- 新增主题变量应在 `constants/theme.ts` 中定义
- 遵循 Ant Design 设计令牌命名规范
- 确保与现有系统的兼容性

### 质量保证

#### 1. 代码审查检查点
- [ ] 是否使用了正确的设计令牌
- [ ] 是否避免了 `!important` 声明
- [ ] 是否遵循了命名约定
- [ ] 是否保持了响应式设计（最小768px）
- [ ] 是否移除了 `xs` 断点配置
- [ ] 媒体查询是否包含 `min-width: 768px`

#### 2. 测试要求
- 验证不同主题下的视觉效果
- 测试响应式布局在768px-2560px分辨率范围内的表现
- 确保在768px最小宽度下所有功能可用
- 确保动画和过渡效果的流畅性
- 验证Grid布局在不同断点下的正确性

### 总结

通过这次全面的主题系统优化，EchoLab 项目已经成功迁移到现代化的设计令牌系统。这不仅提升了代码质量和维护性，也为未来的功能扩展奠定了坚实的基础。所有开发者都应遵循本指南中的原则和最佳实践，确保项目代码的一致性和高质量。

## 🏠 首页布局修复记录

### 问题描述
用户反馈首页布局在特定分辨率下出现错乱，经分析发现是响应式设计未正确支持768px以上分辨率要求。

### 修复内容

#### 1. 响应式媒体查询优化
```css
/* 修复前：包含小于768px的断点 */
@media (max-width: 768px) { }
@media (max-width: 480px) { }

/* 修复后：只支持768px以上 */
@media (max-width: 1024px) and (min-width: 768px) { }
@media (max-width: 900px) and (min-width: 768px) { }
```

#### 2. Grid系统配置修正
```typescript
// 修复前：包含xs断点
<Row gutter={[{ xs: 16, sm: 20, md: 24 }, { xs: 16, sm: 20, md: 24 }]}>
  <Col xs={24} sm={12} md={8} lg={6} xl={4}>

// 修复后：从sm开始
<Row gutter={[{ sm: 20, md: 24, lg: 24 }, { sm: 20, md: 24, lg: 24 }]}>
  <Col sm={12} md={8} lg={6} xl={4}>
```

#### 3. 布局断点策略
| 分辨率范围 | 每行卡片数 | 间距 | 应用场景 |
|-----------|-----------|------|---------|
| 768px-900px | 2个 (sm=12) | 20px | 最小支持分辨率 |
| 901px-1024px | 3个 (md=8) | 24px | 中等屏幕 |
| 1025px-1439px | 4个 (lg=6) | 24px | 大屏幕 |
| 1440px+ | 6个 (xl=4) | 24px | 超大屏幕 |

### 文件修改清单
- `src/renderer/src/pages/HomePage.module.css` - 响应式样式重写
- `src/renderer/src/pages/HomePage.tsx` - Grid配置修正
- `.cursor/rules/dev-guide.mdc` - 开发规范更新

### 验证要点
- [x] 移除所有小于768px的媒体查询
- [x] Grid系统移除xs断点配置
- [x] 添加最小分辨率支持文档
- [x] 更新开发和测试规范

### 影响范围
此修复确保了EchoLab应用在768px以上分辨率下的最佳显示效果，移除了对小屏移动设备的支持以简化维护复杂度。

---

*最后更新: 2024-12-20*
*修复版本: v2024.12*

## 样式系统重构 - 采用 Ant Design v5 Design Token 系统

### 问题背景

项目之前使用了大量自定义 CSS 变量（如 `--darker-bg`、`--text-primary`、`--spacing-*` 等），但这些变量在 `variables.css` 中未定义，导致样式错乱。

### 解决方案

按照 [Ant Design v5 迁移指南](mdc:https:/ant.design/docs/react/migrate-less-variables-cn) 的最佳实践，我们进行了以下重构：

#### 1. 主题配置重构

- **文件**: `src/renderer/src/contexts/ThemeContext.tsx`
- **改进**: 使用 `ConfigProvider` 和标准 Design Token 系统
- **配置方式**: 通过 `theme` 属性配置全局和组件级别的 token

```tsx
<ConfigProvider theme={{
  algorithm: mode === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
  token: {
    colorPrimary: '#667eea',
    borderRadius: 8,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segui UI", Roboto...',
    // 更多 token 配置...
  },
  components: {
    Layout: { bodyBg: '#0f0f0f' },
    Menu: { itemSelectedBg: 'rgba(102, 126, 234, 0.15)' },
    // 组件级别配置...
  }
}}>
```

#### 2. CSS 变量标准化

- **移除自定义变量**: 删除 `--darker-bg`、`--spacing-*`、`--font-size-*` 等
- **使用标准 Token**: 改用 `var(--ant-color-*)`, `var(--ant-size-*)` 等
- **变量映射关系**:
  - `--darker-bg` → `var(--ant-color-bg-layout)`
  - `--text-primary` → `var(--ant-color-text)`
  - `--spacing-lg` → `var(--ant-size-lg)`
  - `--font-size-base` → `var(--ant-font-size-base)`

#### 3. 组件样式更新

已更新的主要组件样式：
- `PlayPage.module.css`: 背景色和边框样式
- `PlayPageHeader.module.css`: 间距和色彩系统
- `responsive.css`: 响应式断点的变量

### 最佳实践规范

#### 1. 主题配置原则

- **优先使用 Design Token**: 通过 `ConfigProvider` 配置，而非自定义 CSS 变量
- **组件级别配置**: 针对特定组件的样式需求使用 Component Token
- **算法驱动**: 利用 `theme.darkAlgorithm` 等算法自动生成色彩系统

#### 2. CSS 编写规范

```css
/* ✅ 推荐：使用 Ant Design Design Token */
.component {
  background: var(--ant-color-bg-container);
  padding: var(--ant-size-lg);
  border-radius: var(--ant-border-radius);
  color: var(--ant-color-text);
}

/* ❌ 避免：自定义 CSS 变量 */
.component {
  background: var(--custom-bg);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-base);
  color: var(--text-primary);
}
```

#### 3. 组件开发规范

- **主题感知**: 组件应该自动适应亮色/暗色主题
- **Token 优先**: 优先使用现有的 Design Token
- **组件配置**: 特殊需求通过 Component Token 配置
- **响应式设计**: 使用 Ant Design 的断点系统

### 性能优化收益

1. **减少样式计算**: 利用 Ant Design 优化过的 CSS 变量系统
2. **主题切换性能**: 通过算法自动生成，避免大量 CSS 重写
3. **Bundle 大小**: 移除重复的自定义变量定义
4. **开发效率**: 标准化的 Design Token 减少样式决策时间

### 后续维护建议

1. **新组件开发**: 严格遵循 Design Token 系统
2. **样式调试**: 使用 Ant Design 开发工具查看 token 值
3. **主题扩展**: 通过 `ConfigProvider` 扩展，而非自定义 CSS
4. **设计一致性**: 利用 Design Token 保证视觉一致性

### 相关资源

- [Ant Design v5 迁移指南](mdc:https:/ant.design/docs/react/migrate-less-variables-cn)
- [Design Token 详细文档](mdc:https:/ant.design/docs/react/customize-theme-cn)
- [Component Token 配置](mdc:https:/ant.design/docs/react/customize-theme-cn#component-token)

### 常见问题解决

**Q: 如何调试 Design Token 值？**
A: 在浏览器开发者工具中查看 `:root` 下的 CSS 变量，或使用 Ant Design 的主题编辑器。

**Q: 需要自定义颜色怎么办？**
A: 通过 `ConfigProvider` 的 `theme.token.colorPrimary` 等配置，而非 CSS 变量。

**Q: 响应式设计如何处理？**
A: 使用 Ant Design 的断点系统和 `@media` 查询，结合 Design Token。

## 项目架构

### 主题自定义系统

#### 概述
项目实现了完整的主题自定义系统，支持用户动态调整应用外观。

#### 核心组件

1. **ThemeProvider** (`src/renderer/src/contexts/ThemeContext.tsx`)
   - 提供全局主题状态管理
   - 支持预览模式和持久化存储
   - 集成 Ant Design 的主题算法

2. **AppearanceSection** (`src/renderer/src/components/Settings/AppearanceSection.tsx`)
   - 主题自定义界面组件
   - 支持色彩、字体、布局等设置
   - 提供预览和应用功能

#### 主要功能

1. **主题模式切换**
   - 亮色主题 (默认)
   - 暗色主题 (darkAlgorithm)
   - 紧凑主题 (compactAlgorithm)
   - 暗色紧凑主题 (组合算法)

2. **色彩定制**
   - 主色调自定义
   - 成功、警告、错误色定制
   - 支持 Apple 色彩预设

3. **布局设置**
   - 圆角大小调整 (0-16px)
   - 基础字号调整 (12-20px)

4. **预览功能**
   - 实时预览主题效果
   - 可在预览和应用之间切换
   - 支持重置为默认设置

#### 技术实现

1. **状态管理**
   ```typescript
   interface ThemeCustomization {
     colorPrimary: string
     colorSuccess: string
     colorWarning: string
     colorError: string
     borderRadius: number
     fontSize: number
     algorithm: 'default' | 'dark' | 'compact' | 'darkCompact'
   }
   ```

2. **持久化存储**
   - 使用 localStorage 保存用户设置
   - 键名: `echolab-theme-customization`
   - 自动加载已保存的配置

3. **主题生成**
   ```typescript
   const generateThemeConfig = (customization: ThemeCustomization): ThemeConfig => {
     // 根据算法选择基础主题
     // 应用用户自定义设置
     // 返回完整的 Ant Design 主题配置
   }
   ```

#### 使用方法

1. **在组件中使用主题上下文**
   ```typescript
   import { useThemeCustomization } from '@renderer/contexts/ThemeContext'
   
   const {
     customization,
     updateCustomization,
     resetToDefault,
     applyTheme,
     isPreviewMode,
     setPreviewMode
   } = useThemeCustomization()
   ```

2. **应用主题设置**
   ```typescript
   // 更新单个设置
   updateCustomization({ colorPrimary: '#FF0000' })
   
   // 应用当前设置
   applyTheme()
   
   // 重置为默认
   resetToDefault()
   ```

#### 配置说明

1. **主题文件结构**
   ```
   src/renderer/src/
   ├── contexts/
   │   └── ThemeContext.tsx     # 主题上下文
   ├── styles/
   │   └── theme.ts            # 基础主题配置
   └── components/Settings/
       └── AppearanceSection.tsx # 主题设置界面
   ```

2. **集成到应用**
   - 在 `App.tsx` 中使用 `ThemeProvider` 包装整个应用
   - 替换原有的 `ConfigProvider` 配置
   - 支持嵌套的其他 Provider 组件

#### 开发注意事项

1. **类型安全**
   - 所有主题相关接口都有完整的 TypeScript 类型定义
   - 使用 Ant Design 的官方类型

2. **性能优化**
   - 使用 `useCallback` 避免不必要的重渲染
   - 预览模式不会触发不必要的状态更新

3. **用户体验**
   - 提供预览功能让用户在应用前查看效果
   - 支持一键重置避免用户设置错误
   - 持久化存储确保设置在重启后保持

#### 扩展指南

1. **添加新的主题选项**
   - 在 `ThemeCustomization` 接口中添加新字段
   - 在 `generateThemeConfig` 中处理新配置
   - 在 `AppearanceSection` 中添加对应的 UI 控件

2. **添加新的预设主题**
   - 在 `theme.ts` 中定义新的主题配置
   - 在主题生成逻辑中支持新的算法组合

3. **自定义组件主题**
   - 使用 Ant Design 的 `components` 配置
   - 在基础主题文件中添加组件特定的样式

这个主题自定义系统为用户提供了丰富的个性化选项，同时保持了良好的开发体验和性能表现。

## 现有架构

### 状态管理
- 使用 React Context 进行状态管理
- 主要 Context：
  - `ShortcutContext`: 快捷键管理
  - `PlayingVideoContext`: 视频播放状态
  - `SubtitleListContext`: 字幕列表管理
  - `VideoPlayerContext`: 视频播放器控制
  - `ThemeContext`: 主题自定义 (新增)

### 主题系统
- 基于 Ant Design 的主题系统
- 使用 `useTheme` Hook 提供统一的主题访问
- 苹果设计风格的默认主题
- 支持 CSS 变量模式

### 组件架构
- 功能型组件分离
- 使用自定义 Hook 管理业务逻辑
- 统一的类型定义

### 开发规范
- TypeScript 严格模式
- ESLint + Prettier 代码规范
- 组件文档注释使用英文
- Git 提交信息使用英文
- 响应用户时使用中文

### 性能优化
- 使用 `useCallback` 和 `useMemo` 优化渲染
- 条件渲染减少不必要的组件加载
- 性能监控工具集成

### 测试策略
- E2E 测试使用 Playwright
- 单元测试覆盖核心业务逻辑
- 测试工具函数提供统一的测试 ID 管理

这份指南将持续更新，确保团队成员了解项目的最新架构和最佳实践。

# 主题系统优化开发指南 / Theme System Optimization Guide

## 概述 / Overview

本次优化对 `useTheme.ts` 文件进行了全面重构，将原本 2469 行的巨型文件拆分为多个模块化文件，提升了代码的可维护性、可扩展性和性能。

This optimization completely refactored the `useTheme.ts` file, splitting the original 2469-line monolithic file into multiple modular files, improving code maintainability, extensibility, and performance.

## 优化内容 / Optimization Details

### 1. 文件拆分 / File Splitting

#### 原有结构 / Original Structure
```
src/renderer/src/hooks/useTheme.ts (2469 lines)
```

#### 新结构 / New Structure
```
src/renderer/src/styles/theme/
├── types.ts                    # 类型定义 / Type definitions
├── constants.ts                # 常量和配置 / Constants and configurations
├── styleFactories.ts           # 样式工厂函数 / Style factory functions
├── modules/
│   ├── baseStyles.ts          # 基础样式模块 / Base styles module
│   └── subtitleStyles.ts      # 字幕样式模块 / Subtitle styles module
├── index.ts                   # 导出索引 / Export index
└── README.md                  # 使用文档 / Usage documentation
```

### 2. 主要改进 / Key Improvements

#### 模块化架构 / Modular Architecture
- **类型分离**: 将所有 TypeScript 接口定义移至 `types.ts`
- **常量提取**: 将重复使用的样式值提取为常量
- **工厂函数**: 创建可复用的样式生成函数
- **功能分组**: 按功能将样式分组到不同模块

#### 性能优化 / Performance Optimization
- **按需加载**: 支持按需导入特定样式模块
- **减少重复**: 消除重复的样式代码
- **缓存优化**: 使用 `useMemo` 优化样式计算
- **类型安全**: 完整的 TypeScript 类型支持

#### 代码质量 / Code Quality
- **可读性**: 清晰的文件结构和命名规范
- **可维护性**: 易于修改和扩展
- **一致性**: 统一的代码风格和注释规范
- **文档化**: 完整的中英文注释

### 3. 新增功能 / New Features

#### 样式工厂函数 / Style Factory Functions
```typescript
// 按钮样式工厂 / Button style factory
createButtonStyle(token, 'MEDIUM', 'primary')

// 弹窗样式工厂 / Popup style factory
createPopupStyle(token, 'fullscreen')

// 毛玻璃效果工厂 / Glass effect factory
createGlassEffect(token, 'medium')

// 响应式样式工厂 / Responsive style factory
createResponsiveStyle(mobileStyles, tabletStyles, desktopStyles)
```

#### 主题常量 / Theme Constants
```typescript
// 动画配置 / Animation configuration
ANIMATION.DURATION.FAST    // '150ms'
ANIMATION.EASING.APPLE     // 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'

// 阴影配置 / Shadow configuration
SHADOWS.APPLE_CARD.light   // Apple-style card shadow
SHADOWS.APPLE_CARD.heavy   // Heavy shadow for elevation

// 全屏主题 / Fullscreen theme
FULLSCREEN_THEME.BACKGROUND // 'rgba(20, 20, 20, 0.95)'
FULLSCREEN_THEME.TEXT       // 'rgba(255, 255, 255, 0.9)'
```

## 使用方法 / Usage

### 基本使用 / Basic Usage
```typescript
import { useTheme } from '@renderer/hooks/useTheme'

function MyComponent() {
  const { token, styles, utils } = useTheme()
  
  return (
    <div style={styles.pageContainer}>
      <button style={styles.primaryButton}>
        Primary Button
      </button>
    </div>
  )
}
```

### 高级使用 / Advanced Usage
```typescript
import { 
  createButtonStyle, 
  createGlassEffect, 
  SUBTITLE_CONSTANTS 
} from '@renderer/styles/theme'

function CustomComponent() {
  const { token } = useTheme()
  
  const customButtonStyle = createButtonStyle(token, 'LARGE', 'secondary')
  const glassStyle = createGlassEffect(token, 'heavy')
  
  return (
    <div style={{ ...glassStyle, padding: token.paddingLG }}>
      <button style={customButtonStyle}>
        Custom Button
      </button>
    </div>
  )
}
```

### 按需导入 / Tree Shaking
```typescript
// 只导入需要的模块 / Import only needed modules
import { buildSubtitleStyles } from '@renderer/styles/theme/modules/subtitleStyles'
import { SUBTITLE_CONSTANTS } from '@renderer/styles/theme/constants'
```

## 迁移指南 / Migration Guide

### 兼容性 / Compatibility
- ✅ 完全向后兼容现有 API
- ✅ 保持所有现有样式名称
- ✅ 维持相同的使用方式

### 推荐做法 / Best Practices
1. **使用样式工厂**: 优先使用样式工厂函数而非硬编码样式
2. **常量引用**: 使用主题常量而非魔术数字
3. **模块导入**: 根据需要按模块导入样式
4. **类型安全**: 充分利用 TypeScript 类型检查

## 性能提升 / Performance Improvements

### 打包优化 / Bundle Optimization
- **减少体积**: 模块化支持 tree-shaking，减少最终打包体积
- **按需加载**: 只加载实际使用的样式模块
- **重复消除**: 消除重复代码，减少内存占用

### 运行时优化 / Runtime Optimization
- **计算缓存**: 使用 `useMemo` 缓存样式计算结果
- **工厂复用**: 样式工厂函数支持参数化复用
- **类型推导**: 编译时类型检查，减少运行时错误

## 扩展性 / Extensibility

### 添加新模块 / Adding New Modules
```typescript
// 1. 创建新的样式模块 / Create new style module