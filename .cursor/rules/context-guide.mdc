---
description: 
globs: 
alwaysApply: false
---
# React 上下文（Context）创建与组织最佳实践

## 1. 何时使用 Context？

- 需要在多个页面/组件间共享全局状态（如：当前播放视频、主题、用户信息等）。
- 避免 props 层层传递。

## 2. 推荐的 Context 组织结构（最佳实践）

为了避免 React Fast Refresh 问题并保持代码清晰，推荐使用**三文件分离**的结构：

- **类型定义文件**（`xxx-context.ts`）：定义类型和创建 context 实例
- **Provider 组件文件**（`XxxContext.tsx`）：只包含 Provider 组件
- **自定义 hook 文件**（`useXxxContext.ts`）：提供类型安全的 hook

### 目录结构示例

```
contexts/
  playing-video-context.ts         # 类型定义和 context 创建
  PlayingVideoContext.tsx          # Provider 组件
  usePlayingVideoContext.ts        # 自定义 hook
  shortcut-context.ts              # 其他 context 的类型定义
  ShortcutContext.tsx              # 其他 context 的 Provider
  useShortcuts.ts                  # 其他 context 的 hook（可放在 hooks/ 目录）
```

## 3. 创建 Context 步骤（推荐方式）

### 1）创建类型定义文件

```tsx
// playing-video-context.ts
import { createContext } from 'react'
import type { UseFileUploadReturn } from '../hooks/useFileUpload'

export type PlayingVideoContextType = UseFileUploadReturn

export const PlayingVideoContext = createContext<PlayingVideoContextType | null>(null)
```

### 2）创建 Provider 组件

```tsx
// PlayingVideoContext.tsx
import React from 'react'
import { useFileUpload } from '../hooks/useFileUpload'
import { PlayingVideoContext, type PlayingVideoContextType } from './playing-video-context'

export function PlayingVideoProvider({ children }: { children: React.ReactNode }): React.JSX.Element {
  const value: PlayingVideoContextType = useFileUpload()

  return <PlayingVideoContext.Provider value={value}>{children}</PlayingVideoContext.Provider>
}
```

### 3）创建自定义 hook

```tsx
// usePlayingVideoContext.ts
import { useContext } from 'react'
import { PlayingVideoContext, type PlayingVideoContextType } from './playing-video-context'

export function usePlayingVideoContext(): PlayingVideoContextType {
  const context = useContext(PlayingVideoContext)
  if (!context) {
    throw new Error('usePlayingVideoContext 必须在 PlayingVideoProvider 内部使用')
  }
  return context
}
```

### 4）在 App 根组件注入 Provider

```tsx
// App.tsx
import { PlayingVideoProvider } from '@renderer/contexts/PlayingVideoContext'

return <PlayingVideoProvider>{/* 你的页面内容 */}</PlayingVideoProvider>
```

### 5）在任意子组件中使用

```tsx
import { usePlayingVideoContext } from '@renderer/contexts/usePlayingVideoContext'

const { videoFile, handleVideoFileSelect } = usePlayingVideoContext()
```

## 4. 旧版本实现方式（不推荐）

以下是旧版本的单文件实现方式，**不推荐使用**，因为会导致 React Fast Refresh 问题：

```tsx
// PlayingVideoContext.tsx（旧版本，不推荐）
import React, { createContext } from 'react'
import { useFileUpload } from '../hooks/useFileUpload'

export type PlayingVideoContextType = ReturnType<typeof useFileUpload>
export const PlayingVideoContext = createContext<PlayingVideoContextType | undefined>(undefined)

export const PlayingVideoProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const value = useFileUpload()
  return <PlayingVideoContext.Provider value={value}>{children}</PlayingVideoContext.Provider>
}
```

## 5. 最佳实践要点

### ✅ 推荐做法：

- **三文件分离**：类型定义、Provider 组件、自定义 hook 分别放在不同文件
- **使用 `null` 作为默认值**：而不是 `undefined`，更符合 React 规范
- **函数声明**：Provider 使用函数声明而不是箭头函数
- **明确返回类型**：Provider 函数明确指定 `React.JSX.Element` 返回类型
- **统一错误消息**：使用中文错误消息保持一致性
- **类型安全**：在 hook 中进行 null 检查，确保类型安全

### ❌ 避免的做法：

- 在同一文件中同时导出 context 和 Provider（会导致 Fast Refresh 问题）
- 使用 `undefined` 作为 context 默认值
- 缺少 null 检查的 hook
- 混合使用中英文错误消息

## 6. 注意事项

- Provider 必须包裹所有需要访问 context 的组件
- 三文件分离结构避免了 React Fast Refresh 报错
- 类型导出时使用 `type` 关键字，避免类型丢失
- 自定义 hook 可以放在 `contexts/` 目录或 `hooks/` 目录，保持项目一致性

---

如需更多示例或遇到 context 相关问题，欢迎查阅本项目其他 context 文件或联系维护者。
